<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class PlayerInvitation extends Model
{
    use HasFactory;
        protected $fillable = ['coach_id', 'user_id', 'team_id', 'invitation_status','balance_due','program_id', 'balance_assigned'];




public function player()
{
    return $this->belongsTo(User::class, 'user_id');
}
 public function user()
    {
        return $this->belongsTo(User::class, 'user_id');
    }


public function coach()
{
    return $this->belongsTo(User::class, 'coach_id');
}

public function team()
{
    return $this->belongsTo(Team::class, 'team_id');
}




}
