<?php

namespace App\Livewire\Admin;

use App\Models\User;
use Livewire\Component;
use Livewire\WithPagination;

class AllUsers extends Component
{
    use WithPagination;

    public $search = '';
    public $filter = '';
    public $perPage = 10;

    // Configuration for searchable fields and filters
    protected $searchableFields = ['firstName', 'lastName', 'email', 'town', 'grade'];
    protected $filterableFields = ['Town' => 'town', 'Age' => 'age', 'Grade' => 'grade'];
    protected $defaultOrderBy = 'firstName';

    // Define the pagination theme for Livewire
    protected $paginationTheme = 'bootstrap';

    public function updatedSearch()
    {
        $this->resetPage();
    }

    public function updatedFilter()
    {
        $this->resetPage();
    }

    public function updatedPerPage()
    {
        $this->resetPage();
    }

    public function render()
    {
        $users = $this->buildQuery()->paginate($this->perPage);
        return view('livewire.admin.all-users', compact('users'));
    }

    /**
     * Build the main query with all filters applied
     */
    protected function buildQuery()
    {
        return User::query()
            ->where('email', '!=', '<EMAIL>')
            ->when($this->hasSearchTerm(), fn($query) => $this->applySearch($query))
            ->when($this->hasValidFilter(), fn($query) => $this->applyOrdering($query))
            ->unless($this->hasValidFilter(), fn($query) => $query->orderBy($this->defaultOrderBy))
            ->with('roles');
    }

    /**
     * Apply search filters to the query
     */
    protected function applySearch($query)
    {
        $searchTerm = $this->search;

        if ($this->hasValidFilter()) {
            return $this->applySpecificFieldSearch($query, $searchTerm);
        }

        return $this->applyGlobalSearch($query, $searchTerm);
    }

    /**
     * Apply search to a specific field based on filter
     */
    protected function applySpecificFieldSearch($query, $searchTerm)
    {
        $field = $this->getFilterField();

        if ($field) {
            $query->where($field, 'like', "%{$searchTerm}%");
        } else {
            $this->applyDefaultSearch($query, $searchTerm);
        }

        return $query;
    }

    /**
     * Apply global search across all searchable fields
     */
    protected function applyGlobalSearch($query, $searchTerm)
    {
        return $query->where(function ($q) use ($searchTerm) {
            foreach ($this->searchableFields as $field) {
                $q->orWhere($field, 'like', "%{$searchTerm}%");
            }
        });
    }

    /**
     * Apply default search (name and email) when filter doesn't match
     */
    protected function applyDefaultSearch($query, $searchTerm)
    {
        return $query->where(function ($q) use ($searchTerm) {
            $q->where('firstName', 'like', "%{$searchTerm}%")
                ->orWhere('lastName', 'like', "%{$searchTerm}%")
                ->orWhere('email', 'like', "%{$searchTerm}%");
        });
    }

    /**
     * Apply ordering based on filter
     */
    protected function applyOrdering($query)
    {
        $field = $this->getFilterField() ?: $this->defaultOrderBy;
        return $query->orderBy($field);
    }

    /**
     * Get the database field name for the current filter
     */
    protected function getFilterField()
    {
        return $this->filterableFields[$this->filter] ?? null;
    }

    /**
     * Check if there's a search term
     */
    protected function hasSearchTerm()
    {
        return !empty($this->search);
    }

    /**
     * Check if the current filter is valid
     */
    protected function hasValidFilter()
    {
        return !empty($this->filter) && array_key_exists($this->filter, $this->filterableFields);
    }

    /**
     * Get available filters for the view
     */
    public function getAvailableFilters()
    {
        return array_keys($this->filterableFields);
    }

    /**
     * Get searchable fields for the view
     */
    public function getSearchableFields()
    {
        return $this->searchableFields;
    }

    /**
     * Add a new searchable field
     */
    public function addSearchableField($field)
    {
        if (!in_array($field, $this->searchableFields)) {
            $this->searchableFields[] = $field;
        }
        return $this;
    }

    /**
     * Add a new filter
     */
    public function addFilter($label, $field)
    {
        $this->filterableFields[$label] = $field;
        return $this;
    }

    /**
     * Clear all filters and search
     */
    public function clearFilters()
    {
        $this->search = '';
        $this->filter = '';
        $this->resetPage();
    }

    /**
     * Edit user - redirect to appropriate edit route based on role
     */
    public function editUser($userId, $role)
    {
        // You can customize these routes based on your application structure
        switch ($role) {
            case 'admin':
                return redirect()->route('admin.admins.edit', $userId);
            case 'coach':
                return redirect()->route('admin.coaches.edit', $userId);
            case 'guardian':
                return redirect()->route('admin.guardians.edit', $userId);
            case 'player':
                return redirect()->route('admin.players.edit', $userId);
            default:
                session()->flash('error', 'Invalid user role.');
        }
    }

    /**
     * Confirm and delete user
     */
    public function confirmDelete($userId)
    {
        try {
            $user = User::findOrFail($userId);

            // Add any business logic checks here
            if ($user->email === '<EMAIL>') {
                session()->flash('error', 'Cannot delete super admin user.');
                return;
            }

            $user->delete();

            session()->flash('success', 'User deleted successfully.');

            // Reset to first page if current page becomes empty after deletion
            $currentPageUsers = $this->buildQuery()->paginate($this->perPage);
            if ($currentPageUsers->count() === 0 && $this->getPage() > 1) {
                $this->previousPage();
            }
        } catch (\Exception $e) {
            session()->flash('error', 'Failed to delete user. Please try again.');
        }
    }
}
