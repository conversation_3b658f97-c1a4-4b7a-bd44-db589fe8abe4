<div class="tables_heading table-program table-responsive">
    <table class="table user-management-table">
        <thead>
            <tr>
                <th style="width: 40px" class="py-4">&nbsp;</th>
                <th style="width: 200px" class="py-4">Name</th>
                <th style="width: 150px" class="py-4">Sport</th>
                <th style="width: 80px" class="py-4">Gender</th>
                <th style="width: 60px" class="py-4">Age</th>
                <th style="width: 100px" class="py-4">Dates</th>
                <th style="width: 150px" class="py-4">Days</th>
                <th style="width: 120px" class="py-4">Enrollment</th>
                <th style="width: 100px" class="py-4">Enrolled</th>
                <th style="width: 100px" class="py-4">Cost</th>
                <th style="width: 120px" class="py-4">Payment</th>
                <th style="width: 200px">&nbsp;</th>
            </tr>
        </thead>
    </table>
</div>

<?php $__currentLoopData = $programData; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $program): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
    <div class="tables_collapse_heading table-heading-grey">
        <table class="table table-hover user-management-table">
            <tr>
                <td style="width: 40px">
                    <div class="icon">
                        <i class="bi bi-caret-up-fill toggle-arrow" style="color:#0B4499; cursor: pointer;"
                            data-toggle="<?php echo e($program['program_slug']); ?>"></i>
                    </div>
                </td>
                <td style="width: 200px" class="text-custom"><?php echo e($program['name']); ?></td>
                <td style="width: 150px" class="text-custom"><?php echo e($program['sport']); ?></td>
                <td style="width: 80px" class="text-custom"><?php echo e($program['gender']); ?></td>
                <td style="width: 60px" class="text-custom">
                    <?php echo e($program['age_restriction_from']); ?>-<?php echo e($program['age_restriction_to']); ?></td>
                <td style="width: 100px" class="text-custom">
                    <?php echo e($program['start_date']); ?><br><?php echo e($program['end_date']); ?>

                </td>
                <td style="width: 150px" class="text-custom">
                    <?php echo e(implode(', ', $program['frequency_days'])); ?><br>
                    <?php echo e(implode('-', array_map('ucfirst', explode('-', strtolower($program['frequency']))))); ?>

                </td>
                <td style="width: 120px" class="text-custom"><?php echo e($program['number_of_registers']); ?></td>
                <td style="width: 100px" class="text-custom"><?php echo e($program['registrations']); ?></td>
                <td style="width: 100px" class="text-custom">$<?php echo e($program['cost']); ?></td>
                <td style="width: 120px" class="text-custom"><?php echo e($program['payment']); ?></td>
                <td style="width: 200px">
                    <table class="user-management-actions-table">
                        <tr>
                            <td class="text-center pe-3" data-program-slug="<?php echo e($program['program_slug']); ?>">
                                <i class="bi bi-filetype-xls export-xls-icon"
                                    style="color: #0B4499; font-size: 1.5rem;"></i>
                            </td>
                            <td class="text-center pe-3">
                                <i class="bi bi-printer-fill print-icon"
                                    data-program-slug="<?php echo e($program['program_slug']); ?>"
                                    style="color: #0B4499; font-size: 1.5rem;"></i>
                            </td>
                            <td class="text-center pe-3">
                                <a href="<?php echo e(route('admin.program.edit', $program['program_slug'])); ?>"
                                    class="action edit" id="edit-admin">
                                    <img src="<?php echo e(asset('images/edit-icon.svg')); ?>" alt="Edit" width="20"
                                        height="20" />
                                </a>
                            </td>
                            <td class="text-center">
                                <form id="deleteProgram-<?php echo e($program['program_slug']); ?>"
                                    onsubmit="showConfirmation(event, 'deleteProgram-<?php echo e($program['program_slug']); ?>')"
                                    action="<?php echo e(route('admin.program.destroy', $program['program_slug'])); ?>"
                                    method="POST" class="delete-form">
                                    <?php echo csrf_field(); ?>
                                    <?php echo method_field('DELETE'); ?>
                                    <button type="submit" class="action edit bg-transparent border-0 p-0">
                                        <img src="<?php echo e(asset('images/delete-icon.svg')); ?>" alt="Delete" width="20"
                                            height="20" />
                                    </button>
                                </form>
                            </td>
                        </tr>
                    </table>
                </td>
            </tr>
        </table>
    </div>
    <div class="table_collapse_table table-program table-responsive table-grey">
        <?php if($program['type'] == 'Team'): ?>
            <table class="table table-hover team-table d-none" width="100%"
                id="program-table<?php echo e($program['program_slug']); ?>">
                <tr>
                    <th width="40">
                        &nbsp;</th>
                    <th width="200">Team Name</th>
                    <th width="200">Coach</th>
                    <th width="250">Email</th>
                    <th width="250">Assistant Coach Email</th>
                    <th width="200">Player Count</th>
                    <th width="200">Team Balance</th>
                    <th width="200" class="empty">&nbsp;</th>
                </tr>
                <?php $__currentLoopData = $program['teams']; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $team): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                    <tr>
                        <td>&nbsp;</td>
                        <td><?php echo e($team['team_name']); ?></td>
                        <td><?php echo e($team['coach_firstName']); ?> <?php echo e($team['coach_lastName']); ?></td>

                        <td><?php echo e($team['coach_email']); ?></td>
                        <td><?php echo e($team['assistantCoachEmail'] ?? 'N/A'); ?></td>
                        <td class="pl-1"><?php echo e($team['player_count']); ?></td>
                        <td>$<?php echo e(number_format($team['team_balance'], 2)); ?></td>
                        <td>
                            <table>
                                <tr>
                                    <td class="pe-5">
                                        <a href="<?php echo e(route('admin.editTeams', ['program' => $program['program_slug'], 'team' => $team['team_id'], 'coach' => $team['coach_slug']])); ?>"
                                            class="action edit">
                                            <img src="<?php echo e(asset('images/edit-icon.svg')); ?>" alt="Edit"
                                                width="20" height="20" />
                                        </a>
                                    </td>
                                    <td>
                                        <form id="removeTeamFromProgram-<?php echo e($team['team_id']); ?>"
                                            onsubmit="showConfirmation(event, 'removeTeamFromProgram-<?php echo e($team['team_id']); ?>')"
                                            action="<?php echo e(route('admin.removeTeamFromProgram', ['program' => $program['program_slug'], 'team' => $team['team_id'], 'coach' => $team['coach_slug']])); ?>"
                                            method="POST" class="inline-form mb-0">
                                            <?php echo csrf_field(); ?>
                                            <?php echo method_field('DELETE'); ?>
                                            <button type="submit" class="action edit bg-transparent border-0 p-0">
                                                <img src="<?php echo e(asset('images/delete-icon.svg')); ?>" alt="Delete"
                                                    width="18" height="20" />
                                            </button>
                                        </form>
                                    </td>

                                </tr>
                            </table>
                        </td>
                    </tr>
                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
            </table>
            <button class="cta openModalButton d-none" id="add-team-btn<?php echo e($program['program_slug']); ?>"
                data-program-id=<?php echo e($program['program_slug']); ?>

                style="font-size: 10px; line-height: 1; font-family: 'Poppins', sans-serif; margin-left: 50px; margin-bottom: 20px;">
                ADD Team to program
            </button>
        <?php elseif($program['type'] == 'Individual' || $program['type'] == 'AAU' || $program['type'] == 'Tryout'): ?>
            <table class="table table-hover team-table d-none" width="100%"
                id="program-table<?php echo e($program['program_slug']); ?>">
                <tr>
                    <th width="40">&nbsp;</th>
                    <th width="200">Player Name</th>
                    <th width="200">Email</th>
                    <th width="200">Guardian Email</th>
                    <th width="200">Program Dates</th>
                    <th width="200" class="empty"></th>
                    <th width="200" class="empy"></th>
                    <th width="200" class="empty">&nbsp;</th>
                </tr>
                <?php $__currentLoopData = $program['players']; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $individual): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                    <tr>
                        <td>&nbsp;</td>
                        <td><?php echo e($individual['player_name']); ?></td>
                        <td><?php echo e($individual['player_email']); ?></td>
                        <td><?php echo e($individual['guardian_email']); ?></td>
                        <td><?php echo e($individual['program_dates']['start_date']); ?> -
                            <?php echo e($individual['program_dates']['end_date']); ?></td>
                        <td></td>
                        <td></td>
                        <td>
                            <table>
                                <tr>
                                    <td class="pe-5">
                                        <a href="#" class="action edit">
                                            <img src="<?php echo e(asset('images/edit-icon.svg')); ?>" alt="Edit"
                                                width="20" height="20" />
                                        </a>
                                    </td>
                                    <td>
                                        <form id="removePlayerForm-<?php echo e($individual['player_id']); ?>"
                                            action="<?php echo e(route('admin.removePlayerFromProgram', ['program' => $program['program_slug'], 'player' => $individual['player_id']])); ?>"
                                            method="POST"
                                            onsubmit="showConfirmation(event, 'removePlayerForm-<?php echo e($individual['player_id']); ?>')"
                                            class="inline-form mb-0">
                                            <?php echo csrf_field(); ?>
                                            <?php echo method_field('DELETE'); ?>
                                            <button type="submit" class="action edit bg-transparent border-0 p-0">
                                                <img src="<?php echo e(asset('images/delete-icon.svg')); ?>" alt="Delete"
                                                    width="18" height="20" />
                                            </button>
                                        </form>
                                    </td>
                                </tr>
                            </table>
                        </td>
                    </tr>
                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>



                
            </table>
            <button class="cta playerModalButton d-none" id="add-player-btn<?php echo e($program['program_slug']); ?>"
                data-program-id=<?php echo e($program['program_slug']); ?>

                style="font-size: 10px; line-height: 1; font-family: 'Poppins', sans-serif; margin-left: 50px; margin-bottom: 20px;">
                ADD player to program
            </button>
        <?php endif; ?>
    </div>
<?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
<?php /**PATH X:\Mass-Premier-Courts\resources\views/admin/reports/partialProgramReport.blade.php ENDPATH**/ ?>