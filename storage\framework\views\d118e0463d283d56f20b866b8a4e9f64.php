<?php $__env->startSection('title', 'Admin'); ?>

<style>
    /* Custom Select2 Styling */
    .select2-container {
        width: 100% !important;
    }

    .select2-container .select2-selection--single {
        box-sizing: border-box;
        cursor: pointer;
        display: block;
        height: 43px;
        user-select: none;
        -webkit-user-select: none;
    }

    .select2-container--default .select2-selection--single {
        background-color: #d9d9d9;
        border-radius: 14px;
        height: 43px;
        border: none;
        padding: 10px 15px;
        font: 500 16px/1 "Montserrat", sans-serif;
    }

    .select2-container--default .select2-selection--single .select2-selection__rendered {
        color: #000;
        line-height: 24px;
    }

    .select2-container--default .select2-selection--single .select2-selection__arrow {
        height: 43px;
        right: 10px;
    }

    .select2-container--default .select2-selection--single .select2-selection__arrow b {
        border-color: #062e69 transparent transparent transparent;
    }

    .select2-dropdown {
        background-color: #fff;
        border: 1px solid #d9d9d9;
        border-radius: 14px;
        box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    }

    .select2-container--default .select2-search--dropdown .select2-search__field {
        border: 1px solid #d9d9d9;
        border-radius: 8px;
        padding: 8px;
    }

    .select2-container--default .select2-results__option--highlighted[aria-selected] {
        background-color: #0b4499;
        color: white;
    }

    .select2-container--default .select2-results__option {
        padding: 8px 12px;
        font-family: "Montserrat", sans-serif;
    }

    .select2-container--default .select2-selection--single .select2-selection__placeholder {
        color: #666;
    }

    /* Fix for Select2 inside Bootstrap modal */
    .modal-open .select2-container--open .select2-dropdown {
        z-index: 1056;
        /* Higher than modal backdrop */
    }

    /* Modal Button Styling */
    #create-team-button {
        margin: 0 auto;
        min-width: 200px;
    }

    .select2-container .select2-selection--single {
        box-sizing: border-box;
        cursor: pointer;
        display: block;
        height: 43px !important;
        user-select: none;
        -webkit-user-select: none
    }

    /* Modal Form Styling */
    #team-create-modal .modal-content {
        border-radius: 20px;
        border: none;
        box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
    }

    #team-create-modal .modal-body {
        padding: 2rem;
    }

    #team-create-modal .form-label {
        margin-bottom: 0.875rem;
        color: #c1b05c;
        font-weight: 900;
        letter-spacing: 2px;
    }

    #team-create-modal .heading h2 {
        color: #062e69;
        font-weight: 900;
        letter-spacing: 2px;
    }

    #team-create-modal .invalid-feedback-admin {
        font-size: 0.875rem;
        margin-top: 0.25rem;
    }

    /* Responsive fixes */
    @media (max-width: 767px) {
        #team-create-modal .modal-dialog {
            margin: 0.5rem;
            max-width: calc(100% - 1rem);
        }

        #team-create-modal .modal-body {
            padding: 1.5rem;
        }

        #create-team-button {
            width: 100%;
        }
    }
</style>

<?php $__env->startSection('content'); ?>

    <section class="page_title text-center pt-5">
        <h1 class="text-uppercase lh-1 mb-0">Programs</h1>
    </section>

    <?php if(session('success')): ?>
        <div id="successMessageForSession">
            <span id="successText"><?php echo e(session('success')); ?></span>
        </div>
    <?php endif; ?>

    <?php if(session('error')): ?>
        <div id="errorMessageForSession">
            <span id="errorText"><?php echo e(session('error')); ?></span>
        </div>
    <?php endif; ?>

    <section class="sec admin-welcome text-center">
        <div class="container">
            <div class="heading align-items-center d-flex flex-column text-center mb-5"><span
                    class="hero-bar d-inline-flex"></span></div>
            <div class="row admin-ctas justify-content-center">
                <div class="col-lg-10">
                    <div class="row justify-content-center">
                        <div class="col-md-6 mt-4"><a
                                class="cta-admin d-flex text-center align-items-center justify-content-center text-uppercase text-decoration-none"
                                href="<?php echo e(route('admin.program.add')); ?>">Add a program</a></div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <section class="sec partition-hr">
        <a class="cta mb-5 ms-5" href="<?php echo e(route('admin.dashboard')); ?>">Back</a>
        <button id="add-the-team" class="cta-button-popup" style="position:absolute; right:2rem;">Create Team</button>

        <div class="container">
            <hr class="border-navy opacity-100 my-0" />
        </div>
    </section>

    <section class="sec program-table">
        <div class="container">
            <div class="heading text-center mb-5">
                <h2 class="text-uppercase fs-6 mb-0">All Programs</h2>
            </div>
            <form class="form row table-filter justify-content-center" id="filterPrograms" method="get">
                <div class="col-md-auto">
                    <div class="filter-option">
                        <div class="mb-4">
                            <label class="form-label text-uppercase mb-4" for="filter">Filter By</label>
                        </div>
                    </div>
                </div>

                <div class="col-md-auto">
                    <div class="filter-option">
                        <div class="mb-4">
                            <label class="form-label text-uppercase" for="sport-filter">Sport</label>
                            <div class="select-arrow position-relative">
                                <select class="form-control" id="sport-filter" name="sport-filter">
                                    <option value="" <?php echo e(request('sport-filter') == '' ? 'selected' : ''); ?>></option>
                                    <option value="basketball"
                                        <?php echo e(request('sport-filter') == 'basketball' ? 'selected' : ''); ?>>Basketball</option>
                                    <option value="volleyball"
                                        <?php echo e(request('sport-filter') == 'volleyball' ? 'selected' : ''); ?>>Volleyball</option>
                                    <option value="pickleball"
                                        <?php echo e(request('sport-filter') == 'pickleball' ? 'selected' : ''); ?>>Pickleball</option>
                                </select>
                                <span class="arrow"><i class="bi bi-chevron-down"></i></span>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="col-md-auto">
                    <div class="filter-option">
                        <div class="mb-4">
                            <label class="form-label text-uppercase" for="gender-filter">Gender</label>
                            <div class="select-arrow position-relative">
                                <select class="form-control" id="gender-filter" name="gender-filter">
                                    <option value="" <?php echo e(request('gender-filter') == '' ? 'selected' : ''); ?>></option>
                                    <option value="boys" <?php echo e(request('gender-filter') == 'boys' ? 'selected' : ''); ?>>Boys
                                    </option>
                                    <option value="girls" <?php echo e(request('gender-filter') == 'girls' ? 'selected' : ''); ?>>
                                        Girls
                                    </option>
                                    <option value="coed" <?php echo e(request('gender-filter') == 'coed' ? 'selected' : ''); ?>>Co-ed
                                    </option>
                                </select>
                                <span class="arrow"><i class="bi bi-chevron-down"></i></span>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="col-md-auto">
                    <div class="filter-option">
                        <div class="mb-4">
                            <label class="form-label text-uppercase" for="age-group-filter">Age Group</label>
                            <div class="select-arrow position-relative">
                                <select class="form-control" id="age-group-filter" name="age-group-filter">
                                    <option value="" <?php echo e(request('age-group-filter') == '' ? 'selected' : ''); ?>>
                                    </option>
                                    <option value="adult" <?php echo e(request('age-group-filter') == 'adult' ? 'selected' : ''); ?>>
                                        Adult
                                    </option>
                                    <option value="u18" <?php echo e(request('age-group-filter') == 'u18' ? 'selected' : ''); ?>>
                                        U18
                                    </option>
                                    <option value="u16" <?php echo e(request('age-group-filter') == 'u16' ? 'selected' : ''); ?>>U16
                                    </option>
                                    <option value="u14" <?php echo e(request('age-group-filter') == 'u14' ? 'selected' : ''); ?>>U14
                                    </option>
                                    <option value="u12" <?php echo e(request('age-group-filter') == 'u12' ? 'selected' : ''); ?>>U12
                                    </option>
                                    <option value="u10" <?php echo e(request('age-group-filter') == 'u10' ? 'selected' : ''); ?>>U10
                                    </option>
                                    <option value="u8" <?php echo e(request('age-group-filter') == 'u8' ? 'selected' : ''); ?>>U8
                                    </option>
                                </select>
                                <span class="arrow"><i class="bi bi-chevron-down"></i></span>
                            </div>
                        </div>
                    </div>
                </div>
            </form>
            <div id="programs-container" class="program-table">
                <!-- This will contain the programs list -->
            </div>

            <div class="program-table" id="loaded-programs">
                <div class="table-program table-responsive">
                    <table class="table table-hover" width="100%">
                        <tbody>
                            <tr>
                                <th class="py-4">Name</th>
                                <th class="py-4">Sport</th>
                                <th class="py-4">Gender</th>
                                <th class="py-4">Age</th>
                                <th class="py-4">Dates</th>
                                <th class="py-4">Days</th>
                                <th class="py-4">Enrollment</th>
                                <th class="py-4">Cost</th>
                                <th class="py-4">Payment</th>
                                <th class="py-4">Status</th>
                                <th class="py-4" width="20"></th>
                                <th class="py-4" width="20"></th>
                            </tr>
                            <?php $__currentLoopData = $programs; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $program): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                <tr>
                                    <td class="py-4 text-capitalize" valign="middle"><?php echo e($program->name); ?></td>
                                    <td class="py-4 text-capitalize" valign="middle"><?php echo e($program->sport); ?></td>
                                    <td class="py-4" valign="middle">
                                        <?php if(strtolower($program->gender) == 'coed'): ?>
                                            Co-ed
                                        <?php else: ?>
                                            <?php echo e(ucfirst($program->gender)); ?>

                                        <?php endif; ?>
                                    </td>

                                    <td class="py-4" valign="middle">
                                        <?php echo e($program->age_restriction_from); ?>-<?php echo e($program->age_restriction_to); ?></td>
                                    <td class="py-4" valign="middle"><?php echo e($program->start_date); ?> -
                                        <?php echo e($program->end_date); ?></td>
                                    <td class="py-4 text-capitalize" valign="middle">
                                        <?php echo e(implode(', ', $program->frequency_days)); ?><br><?php echo e($program->frequency); ?>

                                    </td>
                                    <td class="py-4" valign="middle"><?php echo e($program->number_of_registers); ?></td>
                                    <td class="py-4" valign="middle">$<?php echo e(number_format($program->cost, 2)); ?></td>
                                    <td class="py-4" valign="middle"><?php echo e(ucfirst($program->payment)); ?></td>
                                    <td class="py-4" valign="middle"><?php echo e(ucfirst($program->status)); ?></td>
                                    <td class="py-4" valign="middle">
                                        <span class="action edit">
                                            <a href="<?php echo e(route('admin.program.edit', $program->slug)); ?>">
                                                <img src="<?php echo e(asset('images/edit-icon.svg')); ?>" alt=""
                                                    width="20" height="20" />
                                            </a>
                                        </span>
                                    </td>
                                    <td class="py-4" valign="middle">
                                        <form id="removeProgram-<?php echo e($program->slug); ?>"
                                            action="<?php echo e(route('admin.program.destroy', $program->slug)); ?>" method="POST"
                                            onsubmit="showConfirmation(event, 'removeProgram-<?php echo e($program->slug); ?>')"
                                            style="display: inline;">
                                            <?php echo csrf_field(); ?>
                                            <?php echo method_field('DELETE'); ?>
                                            <button type="submit"
                                                style="border: none; background: none; padding: 0; cursor: pointer;">
                                                <span class="action delete">
                                                    <img src="<?php echo e(asset('images/delete-icon.svg')); ?>" alt=""
                                                        width="18" height="20" />
                                                </span>
                                            </button>
                                        </form>
                                    </td>
                                </tr>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                        </tbody>
                    </table>
                    <div class="mt-4 d-flex justify-content-center pagination">
                        <?php echo e($programs->onEachSide(1)->links('pagination::bootstrap-5')); ?>

                    </div>
                </div>
            </div>
            <div class= "mt-2 d-flex justify-content-center">
                <a class="cta" href="javascript:history.back()">Back</a>
            </div>
        </div>
    </section>

    <!-- Team Creation Modal -->
    <div class="modal fade" id="team-create-modal" tabindex="-1" aria-labelledby="teamCreateLabel" aria-hidden="true"
        data-bs-backdrop="static">
        <div class="modal-dialog modal-dialog-centered" style="max-width: 640px">
            <div class="modal-content">
                <div class="modal-body p-5">
                    <span class="modal-close" data-bs-dismiss="modal" id="cancel-team-create">&#x2715;</span>
                    <div class="heading text-center mb-4">
                        <h2 class="text-uppercase fs-4 mb-0">Create New Team</h2>
                    </div>
                    <form class="form row" id="submit-team-form">
                        <div id="teamSuccessMessage" class="alert alert-success d-none" role="alert"></div>
                        <div id="teamErrorMessage" class="alert alert-danger d-none" role="alert"></div>
                        <?php echo csrf_field(); ?>
                        <div class="mb-4 col-md-12">
                            <label class="form-label text-uppercase" for="teamName">Team Name</label>
                            <input class="form-control" id="teamName" type="text" name="teamName" />
                            <div class="invalid-feedback-admin d-none text-danger" id="teamName-Error"></div>
                        </div>
                        <div class="mb-4 col-md-12">
                            <label class="form-label text-uppercase" for="coachSelect">Select Coach</label>
                            <select class="form-control" id="coachSelect" name="coach_id"></select>
                            <div class="invalid-feedback-admin d-none text-danger" id="coach_id-Error"></div>
                        </div>
                        <div class="mb-4 col-md-12">
                            <label class="form-label text-uppercase" for="programSelect">Select Program</label>
                            <select class="form-control" id="programSelect" name="program_id"></select>
                            <div class="invalid-feedback-admin d-none text-danger" id="program_id-Error"></div>
                        </div>
                        <div class="col-md-12 d-flex align-items-center justify-content-center"
                            style="min-height: 40px; margin-top: 1rem;">
                            <button type="button" class="cta" id="create-team-button">Create Team</button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
<?php $__env->stopSection(); ?>

<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Functions to show session messages
        function showSessionSuccessMessage() {
            const successMessage = document.getElementById('successMessageForSession');
            if (successMessage) {
                setTimeout(() => {
                    successMessage.style.display = 'none';
                }, 5000);
            }
        }

        function showSessionErrorMessage() {
            const errorMessage = document.getElementById('errorMessageForSession');
            if (errorMessage) {
                setTimeout(() => {
                    errorMessage.style.display = 'none';
                }, 5000);
            }
        }

        function handleAjaxRequest(url) {
            const filterForm = document.getElementById('filterPrograms');
            const filterParams = new URLSearchParams(new FormData(filterForm)).toString();

            const separator = url.includes('?') ? '&' : '?';
            const finalUrl = filterParams ? `${url}${separator}${filterParams}` : url;

            document.getElementById('loaded-programs').style.opacity = '0.5';

            fetch(finalUrl, {
                    method: 'GET',
                    headers: {
                        'X-Requested-With': 'XMLHttpRequest',
                    },
                })
                .then((response) => response.json())
                .then((data) => {
                    const alreadyLoadedPrograms = document.getElementById('loaded-programs');
                    alreadyLoadedPrograms.innerHTML = data.programData;
                    alreadyLoadedPrograms.style.opacity = '1';

                    const paginationContainer = document.querySelector('.pagination');
                    if (paginationContainer) {
                        paginationContainer.outerHTML = data.pagination;
                    }

                    window.history.pushState({}, '', finalUrl);
                    bindPaginationLinks();
                })
                .catch((error) => {
                    console.error('Error:', error);
                    document.getElementById('loaded-programs').style.opacity = '1';
                });
        }

        function bindPaginationLinks() {
            document.querySelectorAll('.pagination a.page-link').forEach((link) => {
                link.addEventListener('click', function(e) {
                    e.preventDefault();
                    const url = this.getAttribute('href');
                    handleAjaxRequest(url);
                });
            });
        }

        document.querySelectorAll('#filterPrograms select').forEach(function(select) {
            select.addEventListener('change', function() {
                const form = document.getElementById('filterPrograms');
                const queryString = new URLSearchParams(new FormData(form)).toString();
                const url = "<?php echo e(route('admin.program.allPrograms')); ?>";
                const requestUrl = `${url}?${queryString}`;
                handleAjaxRequest(requestUrl);
            });
        });

        bindPaginationLinks();
        showSessionSuccessMessage();
        showSessionErrorMessage();


        const teamAddButton = document.getElementById('add-the-team');

        teamAddButton.addEventListener('click', function(e) {
            console.log('hitted the button');
        })

        // Team Creation Modal Variables
        const teamModalElement = document.getElementById("team-create-modal");
        const teamForm = document.getElementById("submit-team-form");
        const createTeamButton = document.getElementById("create-team-button");
        const cancelTeamButton = document.getElementById("cancel-team-create");
        const teamSuccessMessage = document.getElementById("teamSuccessMessage");
        const teamErrorMessage = document.getElementById("teamErrorMessage");

        // Initialize modal instance variable
        let teamModalInstance = null;

        // Check if jQuery and Select2 are loaded before initializing
        function initializeSelect2() {
            if (typeof $ !== 'undefined' && typeof $.fn.select2 !== 'undefined') {
                $("#coachSelect").select2({
                    dropdownParent: $("#team-create-modal"),
                    placeholder: "Search for a coach...",
                    allowClear: true,
                    width: '100%',
                    theme: 'default'
                });

                $("#programSelect").select2({
                    dropdownParent: $("#team-create-modal"),
                    placeholder: "Search for a program...",
                    allowClear: true,
                    width: '100%',
                    theme: 'default'
                });
                console.log('Select2 initialized successfully');
            } else {
                console.warn('jQuery or Select2 not loaded. Using regular selects.');
            }
        }

        // Open team creation modal
        document.getElementById("add-the-team").addEventListener("click", function() {
            console.log('Create Team button clicked');

            // Create modal instance if it doesn't exist
            if (!teamModalInstance) {
                teamModalInstance = new bootstrap.Modal(teamModalElement);
            }

            teamModalInstance.show();

            // Initialize Select2 when modal opens
            setTimeout(() => {
                initializeSelect2();
                fetchCoaches();
                fetchTeamPrograms();
            }, 100);
        });

        // Close team creation modal
        cancelTeamButton.addEventListener("click", function() {
            console.log('Cancel button clicked');
            if (teamModalInstance) {
                teamModalInstance.hide();
            }
            clearTeamErrors();
            teamForm.reset();

            // Reset Select2 if available
            if (typeof $ !== 'undefined' && typeof $.fn.select2 !== 'undefined') {
                $("#coachSelect").val(null).trigger("change");
                $("#programSelect").val(null).trigger("change");
            }
        });

        // Handle team creation form submission
        createTeamButton.addEventListener("click", function(event) {
            console.log('Create Team submit button clicked');
            event.preventDefault();
            createTeam();
        });

        // Fetch coaches for dropdown
        function fetchCoaches() {
            console.log('Fetching coaches...');

            // Replace with your actual route - you may need to adjust this
            const coachesUrl = "<?php echo e(route('admin.api.getCoaches') ?? '/admin/api/coaches'); ?>";

            fetch(coachesUrl)
                .then((response) => {
                    if (!response.ok) {
                        throw new Error(`HTTP error! status: ${response.status}`);
                    }
                    return response.json();
                })
                .then((data) => {
                    console.log('Coaches fetched:', data);
                    const coachSelect = document.getElementById("coachSelect");
                    coachSelect.innerHTML = '<option value="">Select a Coach</option>';

                    data.forEach((coach) => {
                        const option = document.createElement("option");
                        option.value = coach.id;
                        option.textContent = `${coach.firstName} ${coach.lastName}`;
                        coachSelect.appendChild(option);
                    });

                    // Refresh Select2 if available
                    if (typeof $ !== 'undefined' && typeof $.fn.select2 !== 'undefined') {
                        $("#coachSelect").trigger("change");
                    }
                })
                .catch((error) => {
                    console.error("Error fetching coaches:", error);
                    teamErrorMessage.textContent = "Failed to load coaches. Please try again.";
                    teamErrorMessage.classList.remove("d-none");
                });
        }

        // Fetch team programs for dropdown
        function fetchTeamPrograms() {
            console.log('Fetching programs...');

            // Replace with your actual route - you may need to adjust this
            const programsUrl = "<?php echo e(route('admin.api.getTeamPrograms') ?? '/admin/api/programs'); ?>";

            fetch(programsUrl)
                .then((response) => {
                    if (!response.ok) {
                        throw new Error(`HTTP error! status: ${response.status}`);
                    }
                    return response.json();
                })
                .then((data) => {
                    console.log('Programs fetched:', data);
                    const programSelect = document.getElementById("programSelect");
                    programSelect.innerHTML = '<option value="">Select a Program</option>';

                    data.forEach((program) => {
                        const option = document.createElement("option");
                        option.value = program.id;
                        option.textContent = program.name;
                        programSelect.appendChild(option);
                    });

                    // Refresh Select2 if available
                    if (typeof $ !== 'undefined' && typeof $.fn.select2 !== 'undefined') {
                        $("#programSelect").trigger("change");
                    }
                })
                .catch((error) => {
                    console.error("Error fetching programs:", error);
                    teamErrorMessage.textContent = "Failed to load programs. Please try again.";
                    teamErrorMessage.classList.remove("d-none");
                });
        }

        // Create team
        async function createTeam() {
            console.log('Creating team...');

            const formData = new FormData(document.getElementById("submit-team-form"));

            // Log form data for debugging
            for (let [key, value] of formData.entries()) {
                console.log(key, value);
            }

            try {
                clearTeamErrors();

                // Replace with your actual route - you may need to adjust this
                const createTeamUrl = "<?php echo e(route('admin.api.createTeam') ?? '/admin/api/teams'); ?>";

                const response = await fetch(createTeamUrl, {
                    method: "POST",
                    headers: {
                        "X-CSRF-TOKEN": document.querySelector('meta[name="csrf-token"]')
                            .getAttribute("content"),
                        "X-Requested-With": "XMLHttpRequest",
                    },
                    body: formData,
                });

                const result = await response.json();
                console.log('Server response:', result);

                if (!response.ok) {
                    console.error('Response not OK:', response.status, result);
                    if (result.errors) {
                        showTeamErrors(result.errors);
                    } else {
                        teamErrorMessage.textContent = result.message ||
                            "An error occurred while creating the team.";
                        teamErrorMessage.classList.remove("d-none");
                    }
                    return;
                }

                if (result.success) {
                    console.log('Team created successfully');
                    showTeamSuccessMessage(result.message);
                    teamForm.reset();

                    // Reset Select2 if available
                    if (typeof $ !== 'undefined' && typeof $.fn.select2 !== 'undefined') {
                        $("#coachSelect").val(null).trigger("change");
                        $("#programSelect").val(null).trigger("change");
                    }

                    // Close modal and reload page after delay
                    setTimeout(() => {
                        if (teamModalInstance) {
                            teamModalInstance.hide();
                        }
                        window.location.reload();
                    }, 2000);
                }
            } catch (error) {
                console.error("Error creating team:", error);
                teamErrorMessage.textContent = "An error occurred while creating the team.";
                teamErrorMessage.classList.remove("d-none");
            }
        }

        // Clear team error messages
        function clearTeamErrors() {
            const errorElements = document.querySelectorAll(".invalid-feedback-admin");
            errorElements.forEach((element) => {
                element.textContent = "";
                element.classList.add("d-none");
            });

            teamErrorMessage.classList.add("d-none");
        }

        // Display team error messages
        function showTeamErrors(errors) {
            console.log('Showing errors:', errors);
            Object.keys(errors).forEach((key) => {
                const errorElement = document.getElementById(`${key}-Error`);
                if (errorElement) {
                    errorElement.textContent = errors[key][0];
                    errorElement.classList.remove("d-none");
                }
            });
        }

        // Display team success message
        function showTeamSuccessMessage(message) {
            console.log('Showing success message:', message);
            teamSuccessMessage.textContent = message;
            teamSuccessMessage.classList.remove("d-none");

            setTimeout(() => {
                teamSuccessMessage.classList.add("d-none");
            }, 3000);
        }

        // Initialize Select2 when document is ready (fallback)
        setTimeout(() => {
            initializeSelect2();
        }, 500);
    });
</script>

<?php echo $__env->make('layouts.app', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH X:\Mass-Premier-Courts\resources\views/admin/allPrograms.blade.php ENDPATH**/ ?>