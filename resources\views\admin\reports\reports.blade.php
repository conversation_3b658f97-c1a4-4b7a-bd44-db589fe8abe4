@extends('layouts.app')
@section('title', 'Admin')
@section('content')

    @if (session('error'))
        <div class="alert alert-danger text-center mx-auto" id="errorSession" role="alert" style="width: 50%;">
            {{ session('error') }}
        </div>
    @endif
    <section class="page_title text-center pt-5">
        <h1 class="text-uppercase lh-1 mb-0">Reports</h1>
    </section>
    <section class="sec report-generate mb-5">
        <div class="container">
            <div class="heading align-items-center d-flex flex-column text-center mb-5">
                <span class="hero-bar d-inline-flex mb-5"></span>
                <div class="heading">
                    <h3 class="mb-0 text-uppercase">Generate...</h3>
                </div>
            </div>
            <div class="row justify-content-center mb-5">
                <div class="col-lg-10">
                    <div class="row justify-content-center report-ctas">
                        <div class="col-md-4 mt-4">
                            <a class="cta-report d-flex text-center align-items-center justify-content-center text-uppercase text-decoration-none"
                                href="{{ route('admin.usersReport') }}"><span class="icon me-2"><img
                                        src="{{ asset('images/ur-icon.svg') }}" alt="user-icon" /></span> User Report</a>
                        </div>
                        <div class="col-md-4 mt-4">
                            <a class="cta-report d-flex text-center align-items-center justify-content-center text-uppercase text-decoration-none"
                                href="{{ route('admin.programsReport') }}"><span class="icon me-2"><img
                                        src="{{ asset('images/pr-icon.svg') }}" alt="program-icon" /></span> PROGRAM
                                Report</a>
                        </div>
                        <div class="col-md-4 mt-4">
                            <a class="cta-report d-flex text-center align-items-center justify-content-center text-uppercase text-decoration-none"
                                href="{{ route('admin.financialReport') }}"><span class="icon me-2"><img
                                        src="{{ asset('images/fr-icon.svg') }}" alt="financial-icon" /></span> FINANCIAL
                                Report</a>
                        </div>
                    </div>
                    <form class="row form mt-5" id="filterForm">
                        <div class="col-md-5 mb-4">
                            <div class="form-label text-uppercase">SORT BY:</div>
                            <div class="row">
                                <div class="mb-2 col-md-6">
                                    <div class="form-check">
                                        <input class="form-check-input filter-checkbox" id="byTown" type="checkbox"
                                            value="town" />
                                        <label class="form-check-label p text-uppercase" for="town">By Town</label>
                                    </div>
                                </div>
                                <div class="mb-2 col-md-6">
                                    <div class="form-check">
                                        <input class="form-check-input filter-checkbox" id="bySport" type="checkbox"
                                            value="sport" />
                                        <label class="form-check-label p text-uppercase" for="sport">By Sport</label>
                                    </div>
                                </div>
                                <div class="mb-2 col-md-6">
                                    <div class="form-check">
                                        <input class="form-check-input filter-checkbox" id="byProgram" type="checkbox"
                                            value="program" />
                                        <label class="form-check-label p text-uppercase" for="program">By Program</label>
                                    </div>
                                </div>
                                <div class="mb-2 col-md-6">
                                    <div class="form-check">
                                        <input class="form-check-input filter-checkbox" id="byPlayer" type="checkbox"
                                            value="player" />
                                        <label class="form-check-label p text-uppercase" for="player">By Player</label>
                                    </div>
                                </div>
                                <div class="mb-2 col-md-6">
                                    <div class="form-check">
                                        <input class="form-check-input filter-checkbox" id="byAge" type="checkbox"
                                            value="age" />
                                        <label class="form-check-label p text-uppercase" for="age">By Age</label>
                                    </div>
                                </div>
                                <div class="mb-2 col-md-6">
                                    <div class="form-check">
                                        <input class="form-check-input filter-checkbox" id="byGender" type="checkbox"
                                            value="gender" />
                                        <label class="form-check-label p text-uppercase" for="gender">By Gender</label>
                                    </div>
                                </div>
                                <div class="mb-2 col-md-6">
                                    <div class="form-check">
                                        <input class="form-check-input filter-checkbox" id="byDate" type="checkbox"
                                            value="date" />
                                        <label class="form-check-label p text-uppercase" for="date">By Date</label>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-5 mb-4">
                            <div class="form-label text-uppercase">FILTER:</div>
                            <div class="mb-4 col-md-12">
                                <label class="form-label text-uppercase text-black fw-normal"
                                    for="category">Category</label>
                                <div class="select-arrow position-relative">
                                    <select class="form-control" id="category">
                                        <option value="">Select Category</option>

                                    </select><span class="arrow"><i class="bi bi-chevron-down"></i></span>
                                </div>
                            </div>
                            <div class="mb-4 col-md-12">
                                <label class="form-label text-uppercase text-black fw-normal" for="Sub category">Sub
                                    Category</label>
                                <div class="select-arrow position-relative">
                                    <select class="form-control" id="subCategory">
                                        <option value="">Select Sub Category</option>

                                    </select><span class="arrow"><i class="bi bi-chevron-down"></i></span>
                                </div>
                            </div>
                        </div>
                        <div class="col-md mb-4">
                            <div class="form-label text-uppercase"></div>
                            <button class="cta rounded-3 w-100" style="min-height: 90px"
                                id="createReport">CREATE</button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </section>
@endsection
<script>
    document.addEventListener('DOMContentLoaded', function() {
        const checkboxes = document.querySelectorAll('.filter-checkbox');
        const categorySelect = document.getElementById('category');
        const subCategorySelect = document.getElementById('subCategory');
        let selectedCheckboxes = [];

        checkboxes.forEach(checkbox => {
            checkbox.addEventListener('change', function() {
                if (this.checked) {
                    if (selectedCheckboxes.length >= 2) {
                        this.checked = false;
                        showGlobalError("You can select two filters");
                        return;
                    } else {
                        selectedCheckboxes.push(this.id);
                    }
                } else {
                    selectedCheckboxes = selectedCheckboxes.filter(id => id !== this.id);
                }

                updateFilters(selectedCheckboxes);
            });
        });

        function updateFilters(selected) {

            categorySelect.style.display = 'block';
            subCategorySelect.style.display = 'block';
            categorySelect.innerHTML = '<option selected value="">Select Category</option>';
            subCategorySelect.innerHTML = '<option selected value="">Select Sub Category</option>';
            categorySelect.name = '';
            subCategorySelect.name = '';
            if (selected.length >= 1) {
                categorySelect.name = selected[0];
            }
            if (selected.length === 2) {
                subCategorySelect.name = selected[1];
            }


            removeDateInputs();


            if (selected.includes('byDate')) {
                if (selected.indexOf('byDate') === 0) {

                    insertDateInputs(categorySelect);
                } else if (selected.indexOf('byDate') === 1) {

                    insertDateInputs(subCategorySelect);
                }
            }


            if (selected.length > 0) {
                const firstOption = selected[0];
                fillDropdown(categorySelect, firstOption);
            }

            if (selected.length > 1) {
                const secondOption = selected[1];
                fillDropdown(subCategorySelect, secondOption);
            }
        }

        function insertDateInputs(targetElement) {
            targetElement.style.display = 'none';

            const dateRangeContainer = document.createElement('div');
            dateRangeContainer.id = 'date-range-container';
            dateRangeContainer.style.marginTop = '10px';
            dateRangeContainer.style.padding = '10px';
            dateRangeContainer.style.border = '1px solid #ccc';
            dateRangeContainer.style.borderRadius = '5px';
            dateRangeContainer.style.backgroundColor = '#f9f9f9';
            dateRangeContainer.style.display = 'flex';
            dateRangeContainer.style.flexWrap = 'wrap';
            dateRangeContainer.style.gap = '20px';

            const startContainer = document.createElement('div');
            startContainer.style.display = 'flex';
            startContainer.style.flexDirection = 'column';

            const startLabel = document.createElement('label');
            startLabel.setAttribute('for', 'startDate');
            startLabel.textContent = 'Start Date:';
            startLabel.style.marginBottom = '5px';

            const startInput = document.createElement('input');
            startInput.type = 'date';
            startInput.id = 'startDate';
            startInput.style.padding = '5px';

            startContainer.appendChild(startLabel);
            startContainer.appendChild(startInput);

            const endContainer = document.createElement('div');
            endContainer.style.display = 'flex';
            endContainer.style.flexDirection = 'column';

            const endLabel = document.createElement('label');
            endLabel.setAttribute('for', 'endDate');
            endLabel.textContent = 'End Date:';
            endLabel.style.marginBottom = '5px';

            const endInput = document.createElement('input');
            endInput.type = 'date';
            endInput.id = 'endDate';
            endInput.style.padding = '5px';

            endContainer.appendChild(endLabel);
            endContainer.appendChild(endInput);

            dateRangeContainer.appendChild(startContainer);
            dateRangeContainer.appendChild(endContainer);

            targetElement.parentNode.insertBefore(dateRangeContainer, targetElement);
        }



        function removeDateInputs() {
            const dateRangeContainer = document.getElementById('date-range-container');
            if (dateRangeContainer) {
                dateRangeContainer.parentNode.removeChild(dateRangeContainer);
            }
        }

        function fillDropdown(dropdown, option) {
            if (dropdown.options.length > 1) return;

            let url;

            if (option === 'byTown') {
                url = route('admin.towns');
            } else if (option === 'bySport') {
                url = route('admin.sport');
            } else if (option === 'byPlayer') {
                url = route('admin.players');
            } else if (option === 'byGender') {
                url = route('admin.genders');
            } else if (option === 'byProgram') {
                url = route('admin.programs');
            } else if (option === 'byAge') {
                url = route('admin.ages');
            }

            if (!url) return;

            fetch(url)
                .then(response => response.json())
                .then(data => {
                    dropdown.innerHTML =
                        '<option selected value="">Select an Option</option>';

                    if (option === 'byTown' || option === 'bySport' || option === 'byGender' || option ===
                        'byProgram') {
                        data.forEach(item => {
                            const optionElement = document.createElement('option');
                            optionElement.value = item.town || item.name || item.gender || item.id;
                            optionElement.textContent = item.town || item.name || item.gender ||
                                item.name;
                            dropdown.appendChild(optionElement);
                        });
                    } else if (option === 'byPlayer') {
                        data.forEach(player => {
                            const optionElement = document.createElement('option');
                            optionElement.value = player.id;
                            optionElement.textContent = `${player.firstName} ${player.lastName}`;
                            dropdown.appendChild(optionElement);
                        });
                    } else if (option === 'byAge') {
                        for (let ageGroup in data) {
                            if (data.hasOwnProperty(ageGroup)) {
                                const optionElement = document.createElement('option');
                                optionElement.value = ageGroup;
                                optionElement.textContent = data[ageGroup];
                                dropdown.appendChild(optionElement);
                            }
                        }
                    }
                })
                .catch(() => {
                    alert(`Failed to fetch data for ${option}.`);
                });
        }



        let form = document.getElementById('filterForm');



        form.addEventListener('submit', function(event) {
            event.preventDefault();
        })


        let createReportButton = document.getElementById('createReport');

        createReportButton.addEventListener('click', function() {
            let category = document.getElementById('category');
            let subCategory = document.getElementById('subCategory');
            let startDateInput = document.getElementById('startDate');
            let endDateInput = document.getElementById('endDate');

            let data = {};
            if (!category.value && !subCategory.value) {
                showGlobalError('Select Values from filters');
                return;
            }

            if (startDateInput && endDateInput) {
                data.startDate = startDateInput.value;
                data.endDate = endDateInput.value;
            }
            if (category && category.value) {
                const categoryName = category.getAttribute('name');
                if (categoryName) {
                    data[categoryName] = category.value;
                }
            }

            if (subCategory && subCategory.value) {
                const subCategoryName = subCategory.getAttribute('name');
                if (subCategoryName) {
                    data[subCategoryName] = subCategory.value;
                }
            }
            const queryString = new URLSearchParams(data).toString();
            let url = route('admin.specificReport') + '?' + queryString;
            fetch(url, {
                    method: 'GET',
                    headers: {
                        'X-Requested-With': 'XMLHttpRequest',
                        'Content-Type': 'application/json',
                    },
                })
                .then((response) => {
                    if (response.ok) {
                        window.location.href = url;
                    } else {
                        return response.json().then((errorData) => {
                            showGlobalError(errorData.message || 'An error occurred');
                        });
                    }
                })
                .catch((error) => {
                    showGlobalError(error.message || 'An error occurred');
                })
        });


        let errorSession = document.getElementById('errorSession');
        if (errorSession) {

            setTimeout(() => {
                errorSession.style.display = 'none';
            }, 3000);
        }

    });
</script>
