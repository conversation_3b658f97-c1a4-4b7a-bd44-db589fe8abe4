<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Team extends Model
{
    use HasFactory;


     protected $fillable = [
        'user_id',
          'name'
    ];



   public function players()
    {
        return $this->belongsToMany(User::class, 'team_player', 'team_id', 'player_id');
    }

    public function teamPrograms()
{
    return $this->hasMany(TeamProgram::class);
}

 public function programs()
    {
        return $this->belongsToMany(Program::class, 'team_program', 'team_id', 'program_id');
    }

 public function coaches()
{
    return $this->belongsToMany(User::class, 'team_coach', 'team_id', 'coach_id')
                ->withPivot('is_primary')
                ->orderBy('team_coach.is_primary', 'asc');
}

public function preferredCoach()
{

    $primaryCoach = $this->coaches()
        ->where('team_coach.is_primary', true)
        ->first();


    if (!$primaryCoach) {
        return $this->coaches()
            ->where('team_coach.is_primary', false)
            ->first();
    }

    return $primaryCoach;
}

}
