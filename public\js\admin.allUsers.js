document.addEventListener("DOMContentLoaded", function () {
    const modalElement = document.getElementById("player-add-modal");
    const playerForm = document.getElementById("submit-Player-Data");
    const addButton = document.getElementById("add-player-button");
    const cancelButton = document.getElementById("cancel-player-add");
    const successMessage = document.getElementById("successMessage");

    // Open modal and fetch guardians
    document
        .getElementById("add-the-player")
        .addEventListener("click", function () {
            const modalInstance = new bootstrap.Modal(modalElement);
            modalInstance.show();
            fetchGuardians();

            document
                .getElementById("uploadIcon")
                .addEventListener("click", function () {
                    document.getElementById("profilePhoto").click();
                });

            document
                .getElementById("profilePhoto")
                .addEventListener("change", function () {
                    const fileInput = document.getElementById("profilePhoto");
                    const fileNameDisplay =
                        document.getElementById("selectedFileName");
                    const imagePreview =
                        document.getElementById("imagePreview");
                    const file = fileInput.files[0];

                    if (file) {
                        fileNameDisplay.textContent = file.name;
                        const reader = new FileReader();
                        reader.onload = function (e) {
                            imagePreview.src = e.target.result;
                            imagePreview.style.display = "block";
                        };
                        reader.readAsDataURL(file);
                    } else {
                        fileNameDisplay.textContent = "No file selected";
                        imagePreview.style.display = "none";
                    }
                });
        });

    cancelButton.addEventListener("click", function () {
        const modalInstance = bootstrap.Modal.getInstance(modalElement);
        modalInstance.hide();
        clearErrors();
        document.getElementById("gender-Error").style.display = "none";
    });
    addButton.addEventListener("click", function (event) {
        event.preventDefault();

        let hasError = false;
        if (guardianSelect.value === "") {
            hasError = true;
            document.getElementById("guardian_id-Error").textContent =
                "Guardian is required.";
            document
                .getElementById("guardian_id-Error")
                .classList.remove("d-none");
        }

        if (hasError) {
            return;
        }
        storePlayer();
    });

    // Fetch guardians for dropdown
    function fetchGuardians() {
        let url = route("admin.api.getGuardians");
        fetch(url)
            .then((response) => response.json())
            .then((data) => {
                const guardianSelect =
                    document.getElementById("guardianSelect");
                guardianSelect.innerHTML =
                    '<option value="">Select a Guardian</option>';

                data.forEach((guardian) => {
                    const option = document.createElement("option");
                    option.value = guardian.id;
                    option.textContent = `${guardian.firstName} ${guardian.lastName}`;
                    guardianSelect.appendChild(option);
                });
            })
            .catch((error) =>
                console.error("Error fetching guardians:", error)
            );
    }

    // Store player
    async function storePlayer() {
        const formData = new FormData(playerForm);
        const url = route("admin.api.store_player");

        try {
            clearErrors();

            const response = await fetch(url, {
                method: "POST",
                headers: {
                    "X-CSRF-TOKEN": "{{ csrf_token() }}",
                    "X-Requested-With": "XMLHttpRequest",
                },
                body: formData,
            });

            const result = await response.json();

            if (!response.ok) {
                if (result.errors) {
                    showErrors(result.errors);
                }
                return;
            }

            if (result.success) {
                showSuccessMessage(result.message);
                playerForm.reset();
                const fileNameDisplay =
                    document.getElementById("selectedFileName");
                const imagePreview = document.getElementById("imagePreview");
                fileNameDisplay.textContent = "No file selected";
                imagePreview.style.display = "none";
            }
        } catch (error) {
            console.error("Error:", error.message);
        }
    }

    // Clear previous error messages
    function clearErrors() {
        const errorElements = document.querySelectorAll(
            ".invalid-feedback-admin"
        );
        errorElements.forEach((element) => {
            element.textContent = "";
            element.classList.add("d-none");
        });
    }

    function showErrors(errors) {
        Object.keys(errors).forEach((key) => {
            const errorElement = document.getElementById(`${key}-Error`);
            if (errorElement) {
                errorElement.textContent = errors[key][0];
                errorElement.style.display = "block";

                errorElement.classList.remove("d-none");
            }
        });
    }

    function showSuccessMessage(message) {
        successMessage.textContent = message;
        successMessage.classList.remove("d-none");

        setTimeout(() => {
            successMessage.classList.add("d-none");
        }, 3000);
    }

    const editModalElement = document.getElementById("player-edit-modal");
    const editPlayerForm = document.getElementById("update-Player-Data");
    const updateButton = document.getElementById("update-player-button");
    const cancelEditButton = document.getElementById("cancel-player-edit");
    const editSuccessMessage = document.getElementById("editSuccessMessage");

    document
        .getElementById("user-table")
        .addEventListener("click", function (event) {
            const button = event.target.closest(".action.edit");

            if (button) {
                if (
                    button.hasAttribute("data-admin-id") ||
                    button.hasAttribute("data-coach-id") ||
                    button.hasAttribute("data-guardian-id") ||
                    button.hasAttribute("data-player-id")
                ) {
                    event.preventDefault();

                    if (button.hasAttribute("data-player-id")) {
                        const playerId = button.getAttribute("data-player-id");

                        openEditModal(playerId);
                    }
                }
            }
        });

    // Open modal and fetch player data
    function openEditModal(playerId) {
        document
            .getElementById("edit-upload-Icon")
            .addEventListener("click", function () {
                document.getElementById("editProfilePhoto").click();
            });

        document
            .getElementById("editProfilePhoto")
            .addEventListener("change", function () {
                const fileInput = document.getElementById("editProfilePhoto");
                const fileNameDisplay = document.getElementById(
                    "editSelectedFileName"
                );
                const editImagePreview =
                    document.getElementById("editImagePreview");
                const file = fileInput.files[0];

                if (file) {
                    fileNameDisplay.textContent = file.name;
                    const reader = new FileReader();
                    reader.onload = function (e) {
                        editImagePreview.src = e.target.result;
                        editImagePreview.style.display = "block";
                    };

                    reader.readAsDataURL(file);
                } else {
                    fileNameDisplay.textContent = "No File Selected";
                    editImagePreview.style.display = "none";
                }
            });

        // Fetch player data
        let url = route("admin.api.show_player", { id: playerId });

        fetch(url)
            .then((response) => response.json())
            .then((data) => {
                const player = data.player;
                const profilePhotoUrl = data.profilePhotoUrl;

                document.getElementById("playerId").value = player.id;
                document.getElementById("editPlayerFirstName").value =
                    player.firstName;
                document.getElementById("editPlayerLastName").value =
                    player.lastName;
                document.getElementById("editEmail").value = player.email;
                document.getElementById("editBirthDate").value =
                    player.birthDate;
                document.getElementById("editGender").value = player.gender;
                document.getElementById("editGrade").value = player.grade;
                document.getElementById("editStreet").value = player.street;
                document.getElementById("editTown").value = player.town;
                document.getElementById("editState").value = player.state;

                getGuardians(player.parent_id);

                if (profilePhotoUrl) {
                    const imagePreview =
                        document.getElementById("editImagePreview");
                    imagePreview.src = profilePhotoUrl;
                    imagePreview.style.display = "block";
                    document.getElementById(
                        "editSelectedFileName"
                    ).textContent = "";
                } else {
                    document.getElementById(
                        "editSelectedFileName"
                    ).textContent = "No file selected";
                    document.getElementById("editImagePreview").style.display =
                        "none";
                }
                const modalInstance = new bootstrap.Modal(editModalElement);
                modalInstance.show();
            })
            .catch((error) =>
                console.error("Error fetching player data:", error)
            );
    }

    // Close modal
    cancelEditButton.addEventListener("click", function () {
        const modalInstance = bootstrap.Modal.getInstance(editModalElement);
        modalInstance.hide();
        resetEditForm();
        clearPlayerValidationErrors();
    });

    function resetEditForm() {
        editPlayerForm.reset();
        document.getElementById("editSelectedFileName").textContent =
            "No file selected";
        const editImagePreview = document.getElementById("editImagePreview");
        editImagePreview.src = "";
        editImagePreview.style.display = "none";
        const errorMessages = editPlayerForm.querySelectorAll(".error-message");
        errorMessages.forEach((error) => error.remove());
        const errorInputs = editPlayerForm.querySelectorAll(".is-invalid");
        errorInputs.forEach((input) => input.classList.remove("is-invalid"));
    }
    editModalElement.addEventListener("hidden.bs.modal", resetEditForm);

    function clearPlayerValidationErrors() {
        const errorElements = document.querySelectorAll(".invalid-feedback");
        errorElements.forEach((element) => {
            element.textContent = "";
            element.classList.add("d-none");
        });
    }

    // Update player
    updateButton.addEventListener("click", function (event) {
        event.preventDefault();
        const editformData = new FormData(editPlayerForm);
        const editPlayerId = document.getElementById("playerId").value;
        document
            .querySelectorAll(".invalid-feedback-admin")
            .forEach((element) => {
                element.textContent = "";
                element.classList.add("d-none");
            });
        let url = route("admin.api.edit_player", {
            id: editPlayerId,
        });
        fetch(url, {
            method: "POST",
            headers: {
                "X-CSRF-TOKEN": "{{ csrf_token() }}",
                "X-Requested-With": "XMLHttpRequest",
            },
            body: editformData,
        })
            .then((response) => response.json())
            .then((data) => {
                if (data.success) {
                    showPlayerMessage(
                        "Player updated successfully!",
                        "alert-success"
                    );
                } else if (data.errors) {
                    handleErrors(data.errors);
                } else {
                    showPlayerMessage(
                        data.error || "Failed to update player.",
                        "alert-danger"
                    );
                    editErrorMessage.textContent = data.message;
                    editErrorMessage.classList.remove("d-none");
                }
            });
    });
    function showPlayerMessage(message, alertClass) {
        const playerUpdateMessage = document.getElementById(
            "playerUpdateMessage"
        );
        const blurOverlay = document.getElementById("blurOverlay");
        playerUpdateMessage.textContent = message;
        playerUpdateMessage.className = `alert ${alertClass} text-center`;
        blurOverlay.classList.remove("d-none");
        playerUpdateMessage.classList.remove("d-none");

        setTimeout(() => {
            playerUpdateMessage.classList.add("d-none");
            blurOverlay.classList.add("d-none");

            const modalInstance = bootstrap.Modal.getInstance(editModalElement);
            modalInstance.hide();

            window.location.reload(true);
        }, 1500);
    }

    function handleErrors(errors) {
        Object.keys(errors).forEach((key) => {
            const errorElement = document.getElementById(
                `edit${key.charAt(0).toUpperCase() + key.slice(1)}-Error`
            );

            if (errorElement) {
                errorElement.textContent = errors[key][0];
                errorElement.style.display = "block";
                errorElement.classList.remove("d-none");

                setTimeout(() => {
                    errorElement.style.display = "none";
                    errorElement.classList.add("d-none");
                }, 2000);
            }
        });
    }

    function getGuardians(selectedGuardianId) {
        let url = route("admin.api.guardianOfPlayer");
        fetch(url)
            .then((response) => response.json())
            .then((data) => {
                const editguardianSelect =
                    document.getElementById("editGuardianSelect");
                editguardianSelect.innerHTML =
                    '<option value="">Select a Guardian</option>';

                data.forEach((guardian) => {
                    const option = document.createElement("option");
                    option.value = guardian.id;
                    option.textContent = `${guardian.firstName} ${guardian.lastName}`;
                    if (guardian.id == selectedGuardianId) {
                        option.selected = true;
                    }
                    editguardianSelect.appendChild(option);
                });
            })
            .catch((error) =>
                console.error("Error fetching guardians:", error)
            );
    }

    const coachModalElement = document.getElementById("coach-add-modal");
    const coachForm = document.getElementById("submit-Coach-Data");
    const addCoachButton = document.getElementById("add-coach-button");
    const cancelCoachButton = document.getElementById("cancel-coach-add");
    const coachSuccessMessage = document.getElementById("coachSuccessMessage");

    document
        .getElementById("add-the-coach")
        .addEventListener("click", function () {
            const coachModalInstance = new bootstrap.Modal(coachModalElement);
            coachModalInstance.show();
        });

    cancelCoachButton.addEventListener("click", function () {
        const coachModalInstance =
            bootstrap.Modal.getInstance(coachModalElement);
        coachModalInstance.hide();
        clearCoachErrors();
    });

    addCoachButton.addEventListener("click", function (event) {
        event.preventDefault();
        storeCoach();
    });

    // Store coach data
    async function storeCoach() {
        const formData = new FormData(coachForm);
        let url = route("admin.api.store_coach");

        try {
            clearCoachErrors();

            const response = await fetch(url, {
                method: "POST",
                headers: {
                    "X-CSRF-TOKEN": "{{ csrf_token() }}",
                    "X-Requested-With": "XMLHttpRequest",
                },
                body: formData,
            });

            const result = await response.json();

            if (!response.ok) {
                if (result.errors) {
                    showCoachErrors(result.errors);
                }
                return;
            }

            if (result.success) {
                showCoachSuccessMessage(result.message);
                coachForm.reset();
            }
        } catch (error) {
            console.error("Error:", error.message);
        }
    }

    // Clear previous error messages
    function clearCoachErrors() {
        const errorElements = document.querySelectorAll(
            ".invalid-feedback-coach"
        );
        errorElements.forEach((element) => {
            element.textContent = "";
            element.classList.add("d-none");
        });
    }

    // Display error messages
    function showCoachErrors(errors) {
        Object.keys(errors).forEach((key) => {
            const errorElement = document.getElementById(
                `coach${key.charAt(0).toUpperCase() + key.slice(1)}-Error`
            );
            if (errorElement) {
                errorElement.textContent = errors[key][0];
                errorElement.classList.remove("d-none");
            }
        });
    }

    // Display success message
    function showCoachSuccessMessage(message) {
        coachSuccessMessage.textContent = message;
        coachSuccessMessage.classList.remove("d-none");
        setTimeout(() => {
            coachSuccessMessage.classList.add("d-none");
        }, 3000);
    }

    //Edit Coach

    const editCoachModalElement = document.getElementById("coach-edit-modal");
    // const coachEditButtons = document.querySelectorAll("#edit-coach");

    // coachEditButtons.forEach((button) => {
    //     button.addEventListener("click", function (event) {
    //         const coachId = this.getAttribute("data-coach-id");
    //         event.preventDefault();
    //         loadCoachData(coachId);
    //         openCoachEditModal();
    //     });
    // });

    // document
    //     .getElementById("user-table")
    //     .addEventListener("click", function (event) {
    //         if (event.target.closest(".action.edit[data-coach-id]")) {
    //             event.preventDefault();
    //             const button = event.target.closest(
    //                 ".action.edit[data-coach-id]"
    //             );
    //             const coachId = button.getAttribute("data-coach-id");
    //             loadCoachData(coachId);
    //             openCoachEditModal();
    //         }
    //     });

    document
        .getElementById("user-table")
        .addEventListener("click", function (event) {
            const button = event.target.closest(".action.edit");

            if (button) {
                if (
                    button.hasAttribute("data-admin-id") ||
                    button.hasAttribute("data-coach-id") ||
                    button.hasAttribute("data-guardian-id") ||
                    button.hasAttribute("data-player-id")
                ) {
                    event.preventDefault();

                    if (button.hasAttribute("data-coach-id")) {
                        const coachId = button.getAttribute("data-coach-id");
                        loadCoachData(coachId);
                        openCoachEditModal();
                    }
                }
            }
        });

    function openCoachEditModal() {
        const modalInstance = new bootstrap.Modal(editCoachModalElement);
        modalInstance.show();
    }

    function loadCoachData(coachId) {
        let url = route("admin.api.show_coach", { id: coachId });
        fetch(url)
            .then((response) => response.json())
            .then((coach) => {
                document.getElementById("coachId").value = coach.id;
                document.getElementById("editCoachFirstName").value =
                    coach.firstName;
                document.getElementById("editCoachLastName").value =
                    coach.lastName;
                document.getElementById("editTownOrProgram").value = coach.town;
                document.getElementById("editTeamName").value = coach.teamName;
                document.getElementById("editCoachEmail").value = coach.email;
                document.getElementById("editCoachPassword").value = "";
            })
            .catch((error) =>
                console.error("Error fetching coach data:", error)
            );
    }
    document
        .getElementById("cancel-coach-edit")
        .addEventListener("click", function () {
            const modalInstance = bootstrap.Modal.getInstance(
                document.getElementById("coach-edit-modal")
            );
            modalInstance.hide();
            clearCoachErrors();
        });
    document
        .getElementById("update-coach-button")
        .addEventListener("click", function (event) {
            event.preventDefault();
            const editformData = new FormData(
                document.getElementById("update-Coach-Data")
            );
            const editCoachId = document.getElementById("coachId").value;

            document
                .querySelectorAll(".invalid-feedback-coach")
                .forEach((element) => {
                    element.textContent = "";
                    element.classList.add("d-none");
                });

            let url = route("admin.api.edit_coach", { id: editCoachId });

            fetch(url, {
                method: "POST",
                headers: {
                    "X-CSRF-TOKEN": "{{ csrf_token() }}",
                    "X-Requested-With": "XMLHttpRequest",
                },
                body: editformData,
            })
                .then((response) => response.json())
                .then((data) => {
                    if (data.success) {
                        document.getElementById(
                            "coachEditSuccessMessage"
                        ).textContent = "Coach updated successfully!";
                        document
                            .getElementById("coachEditSuccessMessage")
                            .classList.remove("d-none");
                        setTimeout(() => {
                            document
                                .getElementById("coachEditSuccessMessage")
                                .classList.add("d-none");
                            const modalInstance = bootstrap.Modal.getInstance(
                                document.getElementById("coach-edit-modal")
                            );
                            modalInstance.hide();
                        }, 3000);
                    } else if (data.errors) {
                        handleCoachErrors(data.errors);
                    } else {
                        document.getElementById(
                            "coachEditErrorMessage"
                        ).textContent = data.message;
                        document
                            .getElementById("coachEditErrorMessage")
                            .classList.remove("d-none");
                    }
                })
                .catch((error) =>
                    console.error("Error updating coach:", error)
                );
        });

    function handleCoachErrors(errors) {
        Object.keys(errors).forEach((key) => {
            const errorElement = document.getElementById(
                `editCoach${key.charAt(0).toUpperCase() + key.slice(1)}-Error`
            );
            if (errorElement) {
                errorElement.textContent = errors[key];
                errorElement.classList.remove("d-none");
            }
        });
    }

    const adminModalElement = document.getElementById("admin-add-modal");
    const adminForm = document.getElementById("submit-Admin-Data");
    const addAdminButton = document.getElementById("add-admin-button");
    const cancelAdminButton = document.getElementById("cancel-admin-add");
    const adminSuccessMessage = document.getElementById("adminSuccessMessage");
    document
        .getElementById("add-the-admin")
        .addEventListener("click", function () {
            const adminModalInstance = new bootstrap.Modal(adminModalElement);
            adminModalInstance.show();
        });

    cancelAdminButton.addEventListener("click", function () {
        const adminModalInstance =
            bootstrap.Modal.getInstance(adminModalElement);
        adminModalInstance.hide();
        clearAdminErrors();
    });

    addAdminButton.addEventListener("click", function (event) {
        event.preventDefault();
        storeAdmin();
    });
    async function storeAdmin() {
        const formData = new FormData(adminForm);
        const url = route("admin.api.store_admin");

        try {
            clearAdminErrors();

            const response = await fetch(url, {
                method: "POST",
                headers: {
                    "X-CSRF-TOKEN": "{{ csrf_token() }}",
                    "X-Requested-With": "XMLHttpRequest",
                },
                body: formData,
            });

            const result = await response.json();

            if (!response.ok) {
                if (result.errors) {
                    showAdminErrors(result.errors);
                }
                return;
            }

            if (result.success) {
                showAdminSuccessMessage(result.message);
                adminForm.reset();
            }
        } catch (error) {
            console.error("Error:", error.message);
        }
    }
    function clearAdminErrors() {
        const errorElements = document.querySelectorAll(
            ".invalid-feedback-admin"
        );
        errorElements.forEach((element) => {
            element.textContent = "";
            element.classList.add("d-none");
        });
    }
    function showAdminErrors(errors) {
        Object.keys(errors).forEach((key) => {
            const errorElement = document.getElementById(
                `admin${key.charAt(0).toUpperCase() + key.slice(1)}-Error`
            );
            if (errorElement) {
                errorElement.textContent = errors[key][0];
                errorElement.classList.remove("d-none");
            }
        });
    }

    function showAdminSuccessMessage(message) {
        adminSuccessMessage.textContent = message;
        adminSuccessMessage.classList.remove("d-none");

        setTimeout(() => {
            adminSuccessMessage.classList.add("d-none");
        }, 3000);
    }
    const editAdminModalElement = document.getElementById("admin-edit-modal");
    // const adminEditButtons = document.querySelectorAll("#edit-admin");

    // adminEditButtons.forEach((button) => {
    //     button.addEventListener("click", function (event) {
    //         const adminId = this.getAttribute("data-admin-id");
    //         event.preventDefault();
    //         loadAdminData(adminId);
    //         openAdminEditModal();
    //     });
    // });

    document
        .getElementById("user-table")
        .addEventListener("click", function (event) {
            const button = event.target.closest(".action.edit");

            if (button) {
                if (
                    button.hasAttribute("data-admin-id") ||
                    button.hasAttribute("data-coach-id") ||
                    button.hasAttribute("data-guardian-id") ||
                    button.hasAttribute("data-player-id")
                ) {
                    event.preventDefault();

                    if (button.hasAttribute("data-admin-id")) {
                        const adminId = button.getAttribute("data-admin-id");
                        loadAdminData(adminId);
                        openAdminEditModal(adminId);
                    }
                }
            }
        });

    function openAdminEditModal() {
        const modalInstance = new bootstrap.Modal(editAdminModalElement);
        modalInstance.show();
    }

    function loadAdminData(adminId) {
        let url = route("admin.api.show_admin", { id: adminId });
        fetch(url)
            .then((response) => response.json())
            .then((admin) => {
                document.getElementById("editAdminId").value = admin.id;
                document.getElementById("editAdminFirstName").value =
                    admin.firstName;
                document.getElementById("editAdminLastName").value =
                    admin.lastName;
                document.getElementById("editAdminEmail").value = admin.email;
            })
            .catch((error) =>
                console.error("Error fetching admin data:", error)
            );
    }

    document
        .getElementById("cancel-admin-edit")
        .addEventListener("click", function () {
            const modalInstance = bootstrap.Modal.getInstance(
                document.getElementById("admin-edit-modal")
            );
            modalInstance.hide();
            clearAdminErrors();
        });

    // Update admin
    document
        .getElementById("update-admin-button")
        .addEventListener("click", function (event) {
            event.preventDefault();
            const editformData = new FormData(
                document.getElementById("update-Admin-Data")
            );
            const editAdminId = document.getElementById("editAdminId").value;

            document
                .querySelectorAll(".invalid-feedback-admin")
                .forEach((element) => {
                    element.textContent = "";
                    element.classList.add("d-none");
                });

            let url = route("admin.api.edit_admin", { id: editAdminId });

            fetch(url, {
                method: "POST",
                headers: {
                    "X-CSRF-TOKEN": "{{ csrf_token() }}",
                    "X-Requested-With": "XMLHttpRequest",
                },
                body: editformData,
            })
                .then((response) => response.json())
                .then((data) => {
                    if (data.success) {
                        document.getElementById(
                            "adminEditSuccessMessage"
                        ).textContent = "Admin updated successfully!";
                        document
                            .getElementById("adminEditSuccessMessage")
                            .classList.remove("d-none");
                        setTimeout(() => {
                            document
                                .getElementById("adminEditSuccessMessage")
                                .classList.add("d-none");
                            const modalInstance = bootstrap.Modal.getInstance(
                                document.getElementById("admin-edit-modal")
                            );
                            modalInstance.hide();
                        }, 3000);
                    } else if (data.errors) {
                        // Handle validation errors
                        handleAdminErrors(data.errors);
                    } else {
                        // Handle other error messages
                        document.getElementById(
                            "adminEditErrorMessage"
                        ).textContent = data.message;
                        document
                            .getElementById("adminEditErrorMessage")
                            .classList.remove("d-none");
                    }
                })
                .catch((error) =>
                    console.error("Error updating admin:", error)
                );
        });

    function handleAdminErrors(errors) {
        Object.keys(errors).forEach((key) => {
            const errorElement = document.getElementById(
                `editAdmin${key.charAt(0).toUpperCase() + key.slice(1)}-Error`
            );
            if (errorElement) {
                errorElement.textContent = errors[key];
                errorElement.classList.remove("d-none");
            }
        });
    }

    const guardianModalElement = document.getElementById("guardian-add-modal");
    const guardianForm = document.getElementById("submit-Guardian-Data");
    const addGuardianButton = document.getElementById("add-guardian-button");
    const cancelGuardianButton = document.getElementById("cancel-guardian-add");
    const guardianSuccessMessage = document.getElementById(
        "guardianSuccessMessage"
    );

    document
        .getElementById("add-the-guardian")
        .addEventListener("click", function () {
            const guardianModalInstance = new bootstrap.Modal(
                guardianModalElement
            );
            guardianModalInstance.show();
        });

    //checkbox for making coach

    const makeCoachCheckbox = document.getElementById("makeCoachCheckbox");
    const coachDetails = document.getElementById("coachDetails");

    makeCoachCheckbox.addEventListener("change", () => {
        if (makeCoachCheckbox.checked) {
            coachDetails.classList.remove("d-none");
            coachDetails.classList.add("show");
        } else {
            coachDetails.classList.remove("show");
            coachDetails.classList.add("d-none");
        }
    });

    cancelGuardianButton.addEventListener("click", function () {
        const guardianModalInstance =
            bootstrap.Modal.getInstance(guardianModalElement);
        guardianModalInstance.hide();
        clearGuardianErrors();
    });
    addGuardianButton.addEventListener("click", function (event) {
        event.preventDefault();
        storeGuardian();
    });

    async function storeGuardian() {
        const formData = new FormData(guardianForm);
        const url = route("admin.api.store_guardian");

        const makeCoachCheckbox = document.getElementById("makeCoachCheckbox");

        if (makeCoachCheckbox.checked) {
            const guardianTown = document.getElementById("guardianTown");
            const guardianTeam = document.getElementById("guardianTeam");

            if (guardianTown.value == "") {
                errorElement = document.getElementById("guardianTown-Error");
                errorElement.classList.remove("d-none");
                errorElement.textContent =
                    "The guardian Town field is required";
                return;
            }

            if (guardianTeam.value == "") {
                errorElement = document.getElementById("guardianTeam-Error");
                errorElement.classList.remove("d-none");
                errorElement.textContent =
                    "The guardian Team field is required";
                return;
            }
        }

        try {
            const response = await fetch(url, {
                method: "POST",
                headers: {
                    "X-CSRF-TOKEN": "{{ csrf_token() }}",
                    "X-Requested-With": "XMLHttpRequest",
                },
                body: formData,
            });

            const result = await response.json();

            if (!response.ok) {
                if (result.errors) {
                    showGuardianErrors(result.errors);
                }
                return;
            }

            if (result.success) {
                showGuardianSuccessMessage(result.message);
                guardianForm.reset();
            }
        } catch (error) {
            console.error("Error:", error.message);
        }
    }

    function clearGuardianErrors() {
        const errorElements = document.querySelectorAll(
            ".invalid-feedback-guardian"
        );
        errorElements.forEach((element) => {
            element.textContent = "";
            element.classList.add("d-none");
        });

        const inputs = document.querySelectorAll(
            '#submit-Guardian-Data input[type="text"], #submit-Guardian-Data input[type="email"], #submit-Guardian-Data input[type="password"]'
        );

        inputs.forEach((input) => {
            input.value = "";
            input.classList.remove("is-invalid");
        });

        const checkboxes = document.querySelectorAll(
            '#submit-Guardian-Data input[type="checkbox"]'
        );
        checkboxes.forEach((checkbox) => {
            checkbox.checked = false;
        });

        document.getElementById("coachDetails").classList.add("d-none");
    }

    function showGuardianErrors(errors) {
        Object.keys(errors).forEach((key) => {
            let formattedKey = key.replace(/_/g, "");
            if (key === "mobile_number") {
                formattedKey = "Mobile";
            }
            const errorElement = document.getElementById(
                `guardian${
                    formattedKey.charAt(0).toUpperCase() + formattedKey.slice(1)
                }-Error`
            );

            if (errorElement) {
                errorElement.textContent = errors[key][0];
                errorElement.classList.remove("d-none");

                setTimeout(() => {
                    errorElement.classList.add("d-none");
                    errorElement.textContent = "";
                }, 2000);
            }
        });
    }

    function showGuardianSuccessMessage(message) {
        guardianSuccessMessage.textContent = message;
        guardianSuccessMessage.classList.remove("d-none");

        setTimeout(() => {
            guardianSuccessMessage.classList.add("d-none");
        }, 3000);
    }

    //edit guardian

    const editGuardianModalElement = document.getElementById(
        "guardian-edit-modal"
    );

    document
        .getElementById("user-table")
        .addEventListener("click", function (event) {
            const button = event.target.closest(".action.edit");

            if (button) {
                if (
                    button.hasAttribute("data-admin-id") ||
                    button.hasAttribute("data-coach-id") ||
                    button.hasAttribute("data-guardian-id") ||
                    button.hasAttribute("data-player-id")
                ) {
                    event.preventDefault();

                    if (button.hasAttribute("data-guardian-id")) {
                        const guardianId =
                            button.getAttribute("data-guardian-id");
                        loadGuardianData(guardianId);
                        openGuardianEditModal();
                    }
                }
            }
        });

    function openGuardianEditModal() {
        const modalInstance = new bootstrap.Modal(editGuardianModalElement);
        modalInstance.show();
    }

    function loadGuardianData(guardianId) {
        let url = route("admin.api.show_guardian", { id: guardianId });
        fetch(url)
            .then((response) => response.json())
            .then((guardian) => {
                document.getElementById("editGuardianId").value = guardian.id;
                document.getElementById("editGuardianFirstName").value =
                    guardian.firstName;
                document.getElementById("editGuardianLastName").value =
                    guardian.lastName;
                document.getElementById("editGuardianEmail").value =
                    guardian.email;
                document.getElementById("editGuardianMobile").value =
                    guardian.mobile_number;
            })
            .catch((error) =>
                console.error("Error fetching guardian data:", error)
            );
    }

    document
        .getElementById("cancel-guardian-edit")
        .addEventListener("click", function () {
            const modalInstance = bootstrap.Modal.getInstance(
                editGuardianModalElement
            );
            modalInstance.hide();
            clearGuardianErrors();
        });

    // Update guardian
    document
        .getElementById("update-guardian-button")
        .addEventListener("click", function (event) {
            event.preventDefault();
            const editFormData = new FormData(
                document.getElementById("update-Guardian-Data")
            );
            const editGuardianId =
                document.getElementById("editGuardianId").value;

            document
                .querySelectorAll(".invalid-feedback-guardian")
                .forEach((element) => {
                    element.textContent = "";
                    element.classList.add("d-none");
                });

            let url = route("admin.api.edit_guardian", { id: editGuardianId });

            fetch(url, {
                method: "POST",
                headers: {
                    "X-CSRF-TOKEN": "{{ csrf_token() }}",
                    "X-Requested-With": "XMLHttpRequest",
                },
                body: editFormData,
            })
                .then((response) => response.json())
                .then((data) => {
                    if (data.success) {
                        document.getElementById(
                            "guardianEditSuccessMessage"
                        ).textContent = "Guardian updated successfully!";
                        document
                            .getElementById("guardianEditSuccessMessage")
                            .classList.remove("d-none");
                        setTimeout(() => {
                            document
                                .getElementById("guardianEditSuccessMessage")
                                .classList.add("d-none");
                            const modalInstance = bootstrap.Modal.getInstance(
                                editGuardianModalElement
                            );
                            modalInstance.hide();
                        }, 3000);
                    } else if (data.errors) {
                        handleGuardianErrors(data.errors);
                    } else {
                        document.getElementById(
                            "guardianEditErrorMessage"
                        ).textContent = data.message;
                        document
                            .getElementById("guardianEditErrorMessage")
                            .classList.remove("d-none");
                    }
                })
                .catch((error) =>
                    console.error("Error updating guardian:", error)
                );
        });

    function handleGuardianErrors(errors) {
        Object.keys(errors).forEach((key) => {
            const errorElement = document.getElementById(
                `editGuardian${
                    key.charAt(0).toUpperCase() + key.slice(1)
                }-Error`
            );
            if (errorElement) {
                const errorMessage = Array.isArray(errors[key])
                    ? errors[key][0]
                    : errors[key];
                errorElement.textContent = errorMessage;
                errorElement.classList.remove("d-none");
            }
        });
    }

    const form = document.getElementById("filterForm");
    const searchField = document.getElementById("search");
    const filterField = document.getElementById("filter");

    if (form) {
        const editIconUrl = "/images/edit-icon.svg";
        const deleteIconUrl = "/images/delete-icon.svg";
        const deleteMethod = `<input type="hidden" name="_method" value="DELETE">`;
        const tableContent = document.querySelector(".table-program");

        const showLoader = () => {
            if (tableContent) {
                tableContent.style.opacity = "0.5";
            }
        };

        const hideLoader = () => {
            if (tableContent) {
                tableContent.style.opacity = "1";
            }
        };

        const fetchUsers = (url) => {
            showLoader();

            let deleteUrl = route("admin.destroy");

            fetch(url, {
                method: "GET",
                headers: {
                    "X-CSRF-TOKEN": "{{ csrf_token() }}",
                    "X-Requested-With": "XMLHttpRequest",
                    Accept: "application/json",
                },
            })
                .then((response) => {
                    if (!response.ok) {
                        throw new Error("Network response was not ok");
                    }
                    return response.json();
                })
                .then((data) => {
                    if (Array.isArray(data.users)) {
                        // Update table rows

                        const rows = data.users
                            .map((user) => {
                                const userRole = user.roles.find((role) =>
                                    [
                                        "admin",
                                        "coach",
                                        "guardian",
                                        "player",
                                    ].includes(role.name)
                                );

                                return `
      <tr>
        <td class="py-4" valign="middle">${user.firstName}</td>
        <td class="py-4" valign="middle">${user.lastName}</td>
        <td class="py-4" valign="middle">
          ${user.roles.map((role) => role.name).join(", ")}
        </td>
        <td class="py-4" valign="middle">${user.email || "N/A"}</td>
        <td class="py-4" valign="middle">${user.town || "N/A"}</td>
        <td class="py-4" valign="middle">${user.age || "N/A"}</td>
        <td class="py-4" valign="middle">${user.grade || "N/A"}</td>
        <td class="py-4" valign="middle">
          ${
              userRole
                  ? `
            <a href="/admin/edit/${userRole.name}/${user.id}"
               class="action edit"
               id="edit-${userRole.name}"
               data-${userRole.name}-id="${user.id}">
              <img src="${editIconUrl}" alt="Edit" width="20" height="20" />
            </a>
          `
                  : ""
          }
        </td>
        <td class="py-4" valign="middle">
          <form action="/admin/delete/${user.id}"
                id="deleteUser-${user.id}"
                method="POST"
                class="inline-form mb-0"
                onsubmit="showConfirmation(event, 'deleteUser-${user.id}')">
                            <input type="hidden" name="_token" value="${csrfToken}">
                            ${deleteMethod}
                            <button type="submit" class="action delete bg-transparent border-0 p-0">
                            <img src="${deleteIconUrl}" alt="Delete" width="18" height="20" />
                            </button>
                            </form>
                            </td>
                        </tr>
                        `;
                            })
                            .join("");

                        document.querySelector(
                            ".table-program tbody"
                        ).innerHTML = rows;
                        document.querySelector(
                            ".table-program .pagination"
                        ).innerHTML = data.pagination;

                        document
                            .querySelectorAll(".table-program .pagination a")
                            .forEach((link) => {
                                link.addEventListener("click", function (e) {
                                    e.preventDefault();
                                    const baseUrl = new URL(
                                        this.getAttribute("href"),
                                        window.location.origin
                                    );
                                    const search = searchField.value;
                                    const filter = filterField.value;

                                    baseUrl.searchParams.set("search", search);
                                    baseUrl.searchParams.set("filter", filter);

                                    fetchUsers(baseUrl);
                                });
                            });
                    } else {
                        console.error("Error: `users` is not an array", data);
                    }
                })
                .catch((error) => console.error("Error:", error))
                .finally(() => {
                    hideLoader();
                });
        };

        function debounce(func, delay) {
            let timer;
            return function (...args) {
                clearTimeout(timer);
                timer = setTimeout(() => {
                    func.apply(this, args);
                }, delay);
            };
        }
        const debouncedFetchUsers = debounce(() => {
            form.dispatchEvent(new Event("submit"));
        }, 500);

        // Handle form submission
        form.addEventListener("submit", function (e) {
            e.preventDefault();
            const filter = filterField.value;
            const search = searchField.value;

            const url = new URL(form.action, window.location.origin);
            url.searchParams.set("filter", filter);
            url.searchParams.set("search", search);

            fetchUsers(url);
        });

        searchField.addEventListener("input", debouncedFetchUsers);

        filterField.addEventListener("change", function () {
            form.dispatchEvent(new Event("submit"));
        });
    }

    showSessionSuccessMessage();
    showSessionErrorMessage();

    // Team Creation Modal
    // const teamModalElement = document.getElementById("team-add-modal");
    // const teamForm = document.getElementById("submit-Team-Data");
    // const createTeamButton = document.getElementById("create-team-button");
    // const cancelTeamButton = document.getElementById("cancel-team-add");
    // const teamSuccessMessage = document.getElementById("teamSuccessMessage");
    // const teamErrorMessage = document.getElementById("teamErrorMessage");

    // // Initialize Select2 for searchable dropdowns
    // $(document).ready(function () {
    //     $("#coachSelect").select2({
    //         dropdownParent: $("#team-add-modal"),
    //         placeholder: "Search for a coach...",
    //         allowClear: true,
    //     });

    //     $("#programSelect").select2({
    //         dropdownParent: $("#team-add-modal"),
    //         placeholder: "Search for a program...",
    //         allowClear: true,
    //     });
    // });

    // // Open team creation modal
    // document
    //     .getElementById("add-the-team")
    //     .addEventListener("click", function () {
    //         const teamModalInstance = new bootstrap.Modal(teamModalElement);
    //         teamModalInstance.show();
    //         fetchCoaches();
    //         fetchTeamPrograms();
    //     });

    // // Close team creation modal
    // cancelTeamButton.addEventListener("click", function () {
    //     const teamModalInstance = bootstrap.Modal.getInstance(teamModalElement);
    //     teamModalInstance.hide();
    //     clearTeamErrors();
    //     teamForm.reset();
    // });

    // // Handle team creation form submission
    // createTeamButton.addEventListener("click", function (event) {
    //     event.preventDefault();
    //     createTeam();
    // });

    // // Fetch coaches for dropdown
    // function fetchCoaches() {
    //     fetch(route("admin.api.getCoaches"))
    //         .then((response) => response.json())
    //         .then((data) => {
    //             const coachSelect = document.getElementById("coachSelect");
    //             coachSelect.innerHTML =
    //                 '<option value="">Select a Coach</option>';

    //             data.forEach((coach) => {
    //                 const option = document.createElement("option");
    //                 option.value = coach.id;
    //                 option.textContent = `${coach.firstName} ${coach.lastName}`;
    //                 coachSelect.appendChild(option);
    //             });

    //             // Refresh Select2 after populating options
    //             $("#coachSelect").trigger("change");
    //         })
    //         .catch((error) => console.error("Error fetching coaches:", error));
    // }

    // // Fetch team programs for dropdown
    // function fetchTeamPrograms() {
    //     fetch(route("admin.api.getTeamPrograms"))
    //         .then((response) => response.json())
    //         .then((data) => {
    //             const programSelect = document.getElementById("programSelect");
    //             programSelect.innerHTML =
    //                 '<option value="">Select a Program</option>';

    //             data.forEach((program) => {
    //                 const option = document.createElement("option");
    //                 option.value = program.id;
    //                 option.textContent = program.name;
    //                 programSelect.appendChild(option);
    //             });

    //             // Refresh Select2 after populating options
    //             $("#programSelect").trigger("change");
    //         })
    //         .catch((error) => console.error("Error fetching programs:", error));
    // }

    // // Create team
    // async function createTeam() {
    //     const formData = new FormData(teamForm);

    //     try {
    //         clearTeamErrors();

    //         const response = await fetch(route("admin.api.createTeam"), {
    //             method: "POST",
    //             headers: {
    //                 "X-CSRF-TOKEN": document
    //                     .querySelector('meta[name="csrf-token"]')
    //                     .getAttribute("content"),
    //                 "X-Requested-With": "XMLHttpRequest",
    //             },
    //             body: formData,
    //         });

    //         const result = await response.json();

    //         if (!response.ok) {
    //             if (result.errors) {
    //                 showTeamErrors(result.errors);
    //             }
    //             return;
    //         }

    //         if (result.success) {
    //             showTeamSuccessMessage(result.message);
    //             teamForm.reset();
    //             $("#coachSelect").val(null).trigger("change");
    //             $("#programSelect").val(null).trigger("change");

    //             // Reload the page after a delay
    //             setTimeout(() => {
    //                 window.location.reload();
    //             }, 2000);
    //         }
    //     } catch (error) {
    //         console.error("Error:", error.message);
    //         teamErrorMessage.textContent =
    //             "An error occurred while creating the team.";
    //         teamErrorMessage.classList.remove("d-none");
    //     }
    // }

    // // Clear team error messages
    // function clearTeamErrors() {
    //     const errorElements = document.querySelectorAll(
    //         ".invalid-feedback-admin"
    //     );
    //     errorElements.forEach((element) => {
    //         element.textContent = "";
    //         element.classList.add("d-none");
    //     });

    //     teamErrorMessage.classList.add("d-none");
    // }

    // // Display team error messages
    // function showTeamErrors(errors) {
    //     Object.keys(errors).forEach((key) => {
    //         const errorElement = document.getElementById(`${key}-Error`);
    //         if (errorElement) {
    //             errorElement.textContent = errors[key][0];
    //             errorElement.classList.remove("d-none");
    //         }
    //     });
    // }

    // // Display team success message
    // function showTeamSuccessMessage(message) {
    //     teamSuccessMessage.textContent = message;
    //     teamSuccessMessage.classList.remove("d-none");

    //     setTimeout(() => {
    //         teamSuccessMessage.classList.add("d-none");
    //     }, 3000);
    // }
});
