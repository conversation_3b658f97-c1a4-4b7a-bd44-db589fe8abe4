<div class="container">
    <div class="heading text-center mb-5">
        <h2 class="text-uppercase fs-6 mb-0">All Users</h2>
    </div>

    
    <div class="form row table-filter justify-content-center">
        <div class="col-md-auto">
            <div class="filter-option">
                <div class="mb-4">
                    <label class="form-label text-uppercase" for="filter">Filter By</label>
                    <div class="select-arrow position-relative">
                        <select class="form-control" id="filter" wire:model.live="filter">
                            <option value="">All</option>
                            <!--[if BLOCK]><![endif]--><?php $__currentLoopData = $this->getAvailableFilters(); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $filterOption): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                <option value="<?php echo e($filterOption); ?>"><?php echo e($filterOption); ?></option>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?><!--[if ENDBLOCK]><![endif]-->
                        </select>
                        <span class="arrow"><i class="bi bi-chevron-down"></i></span>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-auto">
            <div class="filter-option">
                <div class="mb-4">
                    <label class="form-label text-uppercase" for="search">Search</label>
                    <div class="flex items-center">
                        <input class="form-control" id="search" type="text"
                            wire:model.live.debounce.500ms="search" placeholder="Search users..." />
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-auto">
            <div class="filter-option">
                <div class="mb-4">
                    <label class="form-label text-uppercase" for="perPage">Per Page</label>
                    <div class="select-arrow position-relative">
                        <select class="form-control" id="perPage" wire:model.live="perPage">
                            <option value="5">5</option>
                            <option value="10">10</option>
                            <option value="25">25</option>
                            <option value="50">50</option>
                        </select>
                        <span class="arrow"><i class="bi bi-chevron-down"></i></span>
                    </div>
                </div>
            </div>
        </div>
    </div>


    <div class="container">
        <div>
            <button id="sendEmailButton" class="cta"> Send Email</button>
        </div>
    </div>

    
    <div class="program-table">
        <div class="table-program table-responsive" wire:loading.class="table-updating">
            <table class="table table-hover" width="100%" id="user-table">
                <thead>
                    <tr>
                        <th class="py-4">First Name</th>
                        <th class="py-4">Last Name</th>
                        <th class="py-4">User Type</th>
                        <th class="py-4">Email</th>
                        <th class="py-4">Town</th>
                        <th class="py-4">Age</th>
                        <th class="py-4">Grade</th>
                        <th class="py-4" width="20">Edit</th>
                        <th class="py-4" width="20">Delete</th>
                    </tr>
                </thead>
                <tbody>
                    <!--[if BLOCK]><![endif]--><?php $__empty_1 = true; $__currentLoopData = $users; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $user): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
                        <tr wire:key="user-<?php echo e($user->id); ?>">
                            <td class="py-4" valign="middle"><?php echo e($user->firstName); ?></td>
                            <td class="py-4" valign="middle"><?php echo e($user->lastName); ?></td>
                            <td class="py-4" valign="middle">
                                <!--[if BLOCK]><![endif]--><?php $__currentLoopData = $user->roles; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $role): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                    <?php echo e($role->name); ?><!--[if BLOCK]><![endif]--><?php if(!$loop->last): ?>
                                        ,
                                    <?php endif; ?><!--[if ENDBLOCK]><![endif]-->
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?><!--[if ENDBLOCK]><![endif]-->
                            </td>
                            <td class="py-4" valign="middle"><?php echo e($user->email ?? 'N/A'); ?></td>
                            <td class="py-4" valign="middle"><?php echo e($user->town ?? 'N/A'); ?></td>
                            <td class="py-4" valign="middle"><?php echo e($user->age ?? 'N/A'); ?></td>
                            <td class="py-4" valign="middle"><?php echo e($user->grade ?? 'N/A'); ?></td>

                            <!--[if BLOCK]><![endif]--><?php if($user->hasRole('admin')): ?>
                                <td class="py-4" valign="middle">
                                    <a href="" class="action edit" id="edit-admin"
                                        data-admin-id="<?php echo e($user->id); ?>">
                                        <img src="<?php echo e(asset('images/edit-icon.svg')); ?>" alt="Edit" width="20"
                                            height="20" />
                                    </a>
                                </td>
                            <?php elseif($user->hasRole('coach')): ?>
                                <td class="py-4" valign="middle">
                                    <a href="" class="action edit" id="edit-coach"
                                        data-coach-id="<?php echo e($user->id); ?>">
                                        <img src="<?php echo e(asset('images/edit-icon.svg')); ?>" alt="Edit" width="20"
                                            height="20" />
                                    </a>
                                </td>
                            <?php elseif($user->hasRole('guardian')): ?>
                                <td class="py-4" valign="middle">
                                    <a href="" id="edit-guardian" data-guardian-id="<?php echo e($user->id); ?>"
                                        class="action edit">
                                        <img src="<?php echo e(asset('images/edit-icon.svg')); ?>" alt="Edit" width="20"
                                            height="20" />
                                    </a>
                                </td>
                            <?php elseif($user->hasRole('player')): ?>
                                <td class="py-4" valign="middle">
                                    <a href="" class="action edit" id="edit-player"
                                        data-player-id="<?php echo e($user->id); ?>">
                                        <img src="<?php echo e(asset('images/edit-icon.svg')); ?>" alt="Edit" width="20"
                                            height="20" />
                                    </a>
                                </td>
                            <?php endif; ?><!--[if ENDBLOCK]><![endif]-->

                            <td class="py-4" valign="middle">
                                <form id="deleteUser-<?php echo e($user->id); ?>"
                                    onsubmit="showConfirmation(event, 'deleteUser-<?php echo e($user->id); ?>')"
                                    action="<?php echo e(route('admin.destroy', ['user' => $user->id])); ?>" method="POST"
                                    class="inline-form mb-0">
                                    <?php echo csrf_field(); ?>
                                    <?php echo method_field('DELETE'); ?>
                                    <button type="submit" class="action edit bg-transparent border-0 p-0">
                                        <img src="<?php echo e(asset('images/delete-icon.svg')); ?>" alt="Delete"
                                            width="18" height="20" />
                                    </button>
                                </form>
                            </td>
                        </tr>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
                            <tr>
                                <td colspan="9" class="text-center py-4">
                                    <!--[if BLOCK]><![endif]--><?php if($search || $filter): ?>
                                        <p class="mb-0">No users found matching your search criteria.</p>
                                        <button class="btn btn-link p-0" wire:click="clearFilters">Clear filters</button>
                                    <?php else: ?>
                                        <p class="mb-0">No users found.</p>
                                    <?php endif; ?><!--[if ENDBLOCK]><![endif]-->
                                </td>
                            </tr>
                        <?php endif; ?><!--[if ENDBLOCK]><![endif]-->
                    </tbody>
                </table>

                
                <div class="mt-4 d-flex justify-content-center">
                    <?php echo e($users->onEachSide(1)->links('custom-pagination')); ?>

                </div>
            </div>
        </div>
    </div>
<?php /**PATH X:\Mass-Premier-Courts\resources\views/livewire/admin/all-users.blade.php ENDPATH**/ ?>