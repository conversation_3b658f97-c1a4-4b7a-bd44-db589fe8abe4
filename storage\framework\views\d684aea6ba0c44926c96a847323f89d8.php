<?php $__env->startSection('title', 'Edit Program'); ?>
<?php $__env->startSection('content'); ?>
    <section class="page_title text-center pt-5">
        <h1 class="text-uppercase lh-1 mb-0">Edit Program</h1>
    </section>

    <?php if(session('success')): ?>
        <div id="successMessageForSession">
            <span id="successText"><?php echo e(session('success')); ?></span>
        </div>
    <?php endif; ?>

    <?php if(session('error')): ?>
        <div id="errorMessageForSession">
            <span id="errorText"><?php echo e(session('error')); ?></span>
        </div>
    <?php endif; ?>

    <?php if(
        $errors->hasAny([
            'early_bird_specials_date',
            'early_bird_from.*',
            'early_bird_to.*',
            'price_before.*',
            'price_on_or_after.*',
        ])): ?>
        <div id="errorMessageForSession">
            If Early Bird Specials have been checked, you must fill in all the details correctly.
        </div>
    <?php endif; ?>

    <div class="backButtonforAddPrograms">
        <a class="cta"
            href="<?php echo e(url()->previous() !== url()->current() ? url()->previous() : route('admin.program.allPrograms')); ?>">
            Back
        </a>
    </div>



    <section class="sec form mb-5">
        <div class="container">
            <div class="heading align-items-center d-flex flex-column text-center mb-5">
                <span class="hero-bar d-inline-flex"></span>
            </div>
            <div class="row justify-content-center">
                <div class="col-xl-10 col-xxl-9">
                    <form class="row" action="<?php echo e(route('admin.program.update', $program->slug)); ?>" method="POST">
                        <?php echo csrf_field(); ?>
                        <?php echo method_field('PUT'); ?>

                        <!-- Program Name -->
                        <div class="mb-4 col-md-6">
                            <label class="text-uppercase form-label" for="name">Program Name</label>
                            <input class="form-control <?php $__errorArgs = ['name'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" id="programName" type="text"
                                name="name" value="<?php echo e(old('name', $program->name)); ?>" />
                            <?php $__errorArgs = ['name'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                <div class="invalid-feedback"><?php echo e($message); ?></div>
                            <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                        </div>

                        <!-- Program Type -->
                        <div class="mb-4 col-md-6">
                            <label class="form-label text-uppercase" for="type">Program Type</label>
                            <div class="select-arrow position-relative">
                                <select class="form-control <?php $__errorArgs = ['type'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" id="type"
                                    name="type">
                                    <option value="" <?php echo e(old('type', $program->type) == '' ? 'selected' : ''); ?>>Select
                                        Program Type</option>
                                    <option value="Individual"
                                        <?php echo e(old('type', $program->type) == 'Individual' ? 'selected' : ''); ?>>Individual
                                    </option>
                                    <option value="Team" <?php echo e(old('type', $program->type) == 'Team' ? 'selected' : ''); ?>>
                                        Team</option>
                                    <option value="AAU" <?php echo e(old('type', $program->type) == 'AAU' ? 'selected' : ''); ?>>AAU
                                    </option>
                                    <option value="Tryout" <?php echo e(old('type', $program->type) == 'Tryout' ? 'selected' : ''); ?>>
                                        Tryout</option>
                                </select>
                                <span class="arrow"><i class="bi bi-chevron-down"></i></span>
                                <?php $__errorArgs = ['type'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                    <div class="invalid-feedback"><?php echo e($message); ?></div>
                                <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                            </div>
                        </div>


                        <!-- Sub Program Name -->
                        <div class="mb-4 col-md-6">
                            <label class="text-uppercase form-label" for="sub_program">Sub Program Name</label>
                            <input class="form-control <?php $__errorArgs = ['sub_program'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" id="subProgramName"
                                name="sub_program" type="text"
                                value="<?php echo e(old('sub_program', $program->sub_program)); ?>" />
                            <?php $__errorArgs = ['sub_program'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                <div class="invalid-feedback"><?php echo e($message); ?></div>
                            <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                        </div>

                        <!-- Location -->
                        <div class="mb-4 col-md-6">
                            <label class="text-uppercase form-label" for="location">Location</label>
                            <input class="form-control <?php $__errorArgs = ['location'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" id="location"
                                name="location" type="text" value="<?php echo e(old('location', $program->location)); ?>" />
                            <?php $__errorArgs = ['location'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                <div class="invalid-feedback"><?php echo e($message); ?></div>
                            <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                        </div>

                        <!-- Sports -->
                        <div class="mb-4 col-md-6">
                            <label class="form-label text-uppercase" for="sport">Sport</label>
                            <div class="select-arrow position-relative">
                                <select class="form-control <?php $__errorArgs = ['sport'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" id="sports"
                                    name="sport">
                                    <option value="" <?php echo e(old('sport', $program->sport) === '' ? 'selected' : ''); ?>>
                                        Select</option>
                                    <option value="basketball"
                                        <?php echo e(old('sport', $program->sport) === 'basketball' ? 'selected' : ''); ?>>Basketball
                                    </option>
                                    <option value="volleyball"
                                        <?php echo e(old('sport', $program->sport) === 'volleyball' ? 'selected' : ''); ?>>Volleyball
                                    </option>
                                    <option value="pickleball"
                                        <?php echo e(old('sport', $program->sport) === 'pickleball' ? 'selected' : ''); ?>>Pickleball
                                    </option>
                                </select>
                                <span class="arrow"><i class="bi bi-chevron-down"></i></span>
                                <?php $__errorArgs = ['sport'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                    <div class="invalid-feedback"><?php echo e($message); ?></div>
                                <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                            </div>
                        </div>

                        <!-- Gender -->
                        <div class="mb-4 col-md-6">
                            <label class="form-label text-uppercase" for="gender">Gender</label>
                            <div class="select-arrow position-relative">
                                <select class="form-control <?php $__errorArgs = ['gender'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" id="gender"
                                    name="gender">
                                    <option value="" <?php echo e(old('gender', $program->gender) == '' ? 'selected' : ''); ?>>
                                        Select</option>
                                    <option value="boys"
                                        <?php echo e(old('gender', $program->gender) == 'boys' ? 'selected' : ''); ?>>Boys</option>
                                    <option value="girls"
                                        <?php echo e(old('gender', $program->gender) == 'girls' ? 'selected' : ''); ?>>Girls</option>

                                    <option value="coed"
                                        <?php echo e(old('gender', $program->gender) == 'coed' ? 'selected' : ''); ?>>Co-ed</option>
                                </select>
                                <span class="arrow"><i class="bi bi-chevron-down"></i></span>
                                <?php $__errorArgs = ['gender'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                    <div class="invalid-feedback"><?php echo e($message); ?></div>
                                <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                            </div>
                        </div>

                        <!-- Age Restriction -->
                        <div class="col-12 mt-4">
                            <div class="form-label text-uppercase">Age Restriction (optional)</div>
                            <div class="row">
                                <div class="mb-4 col-md-6">
                                    <label class="text-uppercase form-sub-label" for="age_restriction_from">Start
                                        Age</label>
                                    <input class="form-control <?php $__errorArgs = ['age_restriction_from'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>"
                                        id="age_restriction_from" name="age_restriction_from" type="number"
                                        value="<?php echo e(old('age_restriction_from', $program->age_restriction_from)); ?>" />
                                    <?php $__errorArgs = ['age_restriction_from'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                        <div class="invalid-feedback"><?php echo e($message); ?></div>
                                    <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                </div>
                                <div class="mb-4 col-md-6">
                                    <label class="text-uppercase form-sub-label" for="age_restriction_to">End Age</label>
                                    <input class="form-control <?php $__errorArgs = ['age_restriction_to'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>"
                                        id="age_restriction_to" name="age_restriction_to" type="number"
                                        value="<?php echo e(old('age_restriction_to', $program->age_restriction_to)); ?>" />
                                    <?php $__errorArgs = ['age_restriction_to'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                        <div class="invalid-feedback"><?php echo e($message); ?></div>
                                    <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                </div>
                            </div>
                        </div>

                        <!-- Grade -->
                        <div class="mb-4 col-md-6">
                            <label class="form-label text-uppercase" for="grade">Grade</label>
                            <div class="select-arrow position-relative">
                                <select class="form-control <?php $__errorArgs = ['grade'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" id="grade"
                                    name="grade">
                                    <option value="" <?php echo e(old('grade', $program->grade) == '' ? 'selected' : ''); ?>>
                                        Select
                                    </option>
                                    <option value="6th" <?php echo e(old('grade', $program->grade) == '6th' ? 'selected' : ''); ?>>
                                        6th
                                    </option>
                                    <option value="7th" <?php echo e(old('grade', $program->grade) == '7th' ? 'selected' : ''); ?>>
                                        7th
                                    </option>
                                    <option value="8th" <?php echo e(old('grade', $program->grade) == '8th' ? 'selected' : ''); ?>>
                                        8th
                                    </option>
                                    <option value="9th" <?php echo e(old('grade', $program->grade) == '9th' ? 'selected' : ''); ?>>
                                        9th
                                    </option>
                                    <option value="10th"
                                        <?php echo e(old('grade', $program->grade) == '10th' ? 'selected' : ''); ?>>
                                        10th
                                    </option>
                                    <option value="adult"
                                        <?php echo e(old('grade', $program->grade) == 'adult' ? 'selected' : ''); ?>>Adult
                                    </option>
                                </select>
                                <span class="arrow"><i class="bi bi-chevron-down"></i></span>
                                <?php $__errorArgs = ['grade'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                    <div class="invalid-feedback"><?php echo e($message); ?></div>
                                <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                            </div>
                        </div>

                        <!-- Birth Date Cutoff -->
                        <div class="mb-4 col-md-6">
                            <label class="form-label text-uppercase" for="birth_date_cutoff">Birth Date Cutoff</label>
                            <input class="form-control <?php $__errorArgs = ['birth_date_cutoff'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>"
                                id="birth_date_cutoff" name="birth_date_cutoff" type="date"
                                value="<?php echo e(old('birth_date_cutoff', $program->birth_date_cutoff)); ?>" />
                            <?php $__errorArgs = ['birth_date_cutoff'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                <div class="invalid-feedback"><?php echo e($message); ?></div>
                            <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                        </div>

                        <!-- Registration -->
                        <div class="col-12 mt-4">
                            <div class="form-label text-uppercase">Registration</div>
                            <div class="row">
                                <div class="mb-4 col-md-6">
                                    <label class="text-uppercase form-sub-label"
                                        for="registration_opening_date">Registration Start</label>
                                    <input class="form-control <?php $__errorArgs = ['registration_opening_date'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>"
                                        id="registration_opening_date" name="registration_opening_date" type="date"
                                        value="<?php echo e(old('registration_opening_date', $program->registration_opening_date)); ?>" />
                                    <?php $__errorArgs = ['registration_opening_date'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                        <div class="invalid-feedback"><?php echo e($message); ?></div>
                                    <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                </div>
                                <div class="mb-4 col-md-6">
                                    <label class="text-uppercase form-sub-label"
                                        for="registration_closing_date">Registration End</label>
                                    <input class="form-control <?php $__errorArgs = ['registration_closing_date'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>"
                                        id="registration_closing_date" name="registration_closing_date" type="date"
                                        value="<?php echo e(old('registration_closing_date', $program->registration_closing_date)); ?>" />
                                    <?php $__errorArgs = ['registration_closing_date'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                        <div class="invalid-feedback"><?php echo e($message); ?></div>
                                    <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                </div>
                            </div>
                        </div>

                        <!-- Start Date and End Date -->
                        <div class="col-12 mt-4">
                            <div class="row">
                                <div class="mb-4 col-md-6">
                                    <label class="form-label text-uppercase" for="start_date">Start Date</label>
                                    <input class="form-control <?php $__errorArgs = ['start_date'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" id="start_date"
                                        name="start_date" type="date"
                                        value="<?php echo e(old('start_date', $program->start_date)); ?>" />
                                    <?php $__errorArgs = ['start_date'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                        <div class="invalid-feedback"><?php echo e($message); ?></div>
                                    <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                </div>
                                <div class="mb-4 col-md-6">
                                    <label class="form-label text-uppercase" for="end_date">End Date</label>
                                    <input class="form-control <?php $__errorArgs = ['end_date'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" id="end_date"
                                        name="end_date" type="date"
                                        value="<?php echo e(old('end_date', $program->end_date)); ?>" />
                                    <?php $__errorArgs = ['end_date'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                        <div class="invalid-feedback"><?php echo e($message); ?></div>
                                    <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                </div>
                            </div>
                        </div>

                        <!-- Start Time and End Time -->
                        <div class="col-12 mt-4">
                            <div class="row">
                                <div class="mb-4 col-md-6">
                                    <label class="form-label text-uppercase" for="start_time">Start Time</label>
                                    <input class="form-control <?php $__errorArgs = ['start_time'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" id="start_time"
                                        name="start_time" type="time"
                                        value="<?php echo e(old('start_time', $program->start_time)); ?>" />
                                    <?php $__errorArgs = ['start_time'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                        <div class="invalid-feedback"><?php echo e($message); ?></div>
                                    <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                </div>
                                <div class="mb-4 col-md-6">
                                    <label class="form-label text-uppercase" for="end_time">End Time</label>
                                    <input class="form-control <?php $__errorArgs = ['end_time'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" id="end_time"
                                        name="end_time" type="time"
                                        value="<?php echo e(old('end_time', $program->end_time)); ?>" />
                                    <?php $__errorArgs = ['end_time'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                        <div class="invalid-feedback"><?php echo e($message); ?></div>
                                    <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                </div>
                            </div>
                        </div>

                        <!-- Frequency -->
                        <div class="col-12 mt-4">
                            <div class="form-label text-uppercase">Frequency</div>

                            <div class="form-sub-label text-uppercase mb-3">Daily</div>
                            <div class="row">
                                <?php $__currentLoopData = ['mon', 'tue', 'wed', 'thur', 'fri', 'sat', 'sun']; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $day): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                    <div class="mb-4 col-md">
                                        <div class="form-check">
                                            <input class="form-check-input <?php $__errorArgs = ['frequency_days'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>"
                                                id="<?php echo e($day); ?>" name="frequency_days[]" type="checkbox"
                                                value="<?php echo e($day); ?>"
                                                <?php echo e(is_array(old('frequency_days', $program->frequency_days)) && in_array($day, old('frequency_days', $program->frequency_days)) ? 'checked' : ''); ?> />
                                            <label class="form-check-label text-uppercase"
                                                for="<?php echo e($day); ?>"><?php echo e(strtoupper($day)); ?></label>
                                        </div>
                                    </div>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                            </div>

                            <div class="form-sub-label text-uppercase mb-3">Weekly</div>
                            <div class="row">
                                <?php $__currentLoopData = ['every-week', 'every-other-week', 'once-per-month']; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $week): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                    <div class="mb-4 col-md">
                                        <div class="form-check">
                                            <input class="form-check-input <?php $__errorArgs = ['frequency'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>"
                                                id="<?php echo e($week); ?>" name="frequency" type="radio"
                                                value="<?php echo e($week); ?>"
                                                <?php echo e(old('frequency', $program->frequency) === $week ? 'checked' : ''); ?> />
                                            <label class="form-check-label text-uppercase"
                                                for="<?php echo e($week); ?>"><?php echo e(strtoupper(str_replace('-', ' ', $week))); ?></label>
                                        </div>
                                    </div>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                            </div>

                            <?php $__errorArgs = ['frequency'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                <div class="invalid-feedback"><?php echo e($message); ?></div>
                            <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>

                        </div>

                        <div class="col-md-6 mt-4">
                            <div class="form-label text-uppercase">Enrollment (optional)</div>
                            <div class="form-sub-label text-uppercase mb-3">Registration Limit</div>
                            <div class="row align-items-center">
                                <div class="mb-4 col-md-2">
                                    <label class="text-uppercase visually-hidden form-label"
                                        for="number_of_registers">Number of Registers</label>
                                    <input class="form-control <?php $__errorArgs = ['number_of_registers'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>"
                                        id="number_of_registers" name="number_of_registers" type="number"
                                        value="<?php echo e(old('number_of_registers', $program->number_of_registers)); ?>" />
                                    <?php $__errorArgs = ['number_of_registers'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                        <div class="invalid-feedback"><?php echo e($message); ?></div>
                                    <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                </div>
                                <div class="mb-4 col-md">
                                    <div class="form-check">
                                        <input type="hidden" name="enable_waitlist" value="0">
                                        <input class="form-check-input <?php $__errorArgs = ['enable_waitlist'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>"
                                            id="enable-waitlist" name="enable_waitlist" type="checkbox" value="1"
                                            <?php echo e(old('enable_waitlist', $program->enable_waitlist) ? 'checked' : ''); ?> />
                                        <label class="form-check-label p text-uppercase" for="enable-waitlist">Enable
                                            Waitlist</label>
                                    </div>
                                    <?php $__errorArgs = ['enable_waitlist'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                        <div class="invalid-feedback"><?php echo e($message); ?></div>
                                    <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                </div>
                            </div>
                        </div>

                        <!-- Cost -->
                        <div class="col-md-6 mt-4">
                            <div class="form-label text-uppercase">Cost</div>
                            <div class="form-sub-label text-uppercase mb-3">&nbsp;</div>
                            <div class="row align-items-center">
                                <div class="mb-4 col-md-4">
                                    <label class="text-uppercase visually-hidden form-label" for="cost">Cost</label>
                                    <input class="form-control <?php $__errorArgs = ['cost'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>"
                                        id="cost"style="width: 100%;" name="cost" type="number" step="0.01"
                                        value="<?php echo e(old('cost', $program->cost)); ?>" />
                                    <?php $__errorArgs = ['cost'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                        <div class="invalid-feedback"><?php echo e($message); ?></div>
                                    <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                </div>
                                <div class="mb-4 col-md-8">
                                    <div class="form-check col-md-">
                                        <input type="hidden" name="enable_early_bird_specials" value="0">
                                        <input class="form-check-input" id="enable_early_bird_specials" type="checkbox"
                                            value="1" name="enable_early_bird_specials"
                                            <?php echo e(old('enable_early_bird_specials', $program->enable_early_bird_specials) ? 'checked' : ''); ?> />
                                        <label class="form-check-label p text-uppercase" for="enable_early_bird_specials">
                                            Enable Early Bird Specials
                                        </label>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div id="early_bird_pricing_section" class="col-md-12 mt-4 mb-4 to_set_mb3"
                            style="<?php echo e(old('enable_early_bird_specials', $program->enable_early_bird_specials) ? '' : 'display: none;'); ?>">
                            <?php if($earlyBirdPricing->isEmpty()): ?>
                                <div class="col-md-12 mt-4 mb-4 to_set_mb3">
                                    <div class="form-label text-uppercase">Early Bird Pricing</div>
                                    <div class="row align-items-center">
                                        <div class="mb-4 col-md">
                                            <label class="text-uppercase form-sub-label" for="cost">From</label>
                                            <input class="form-control" id="cost" type="number" step="0.01"
                                                name="early_bird_from[]" value="<?php echo e(old('early_bird_from.0')); ?>" />
                                        </div>
                                        <div class="mb-4 col-md">
                                            <label class="text-uppercase form-sub-label" for="to">to</label>
                                            <input class="form-control" id="to" type="number" step="0.01"
                                                name="early_bird_to[]" value="<?php echo e(old('early_bird_to.0')); ?>" />
                                            <?php $__errorArgs = ['early_bird_to.0'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                                <div class="invalid-feedback"><?php echo e($message); ?></div>
                                            <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                        </div>
                                        <div class="mb-4 col-md-3">
                                            <label class="text-uppercase form-sub-label" for="priceBefore">Price
                                                Before</label>
                                            <input class="form-control" id="priceBefore" type="number" step="0.01"
                                                name="price_before[]" value="<?php echo e(old('price_before.0')); ?>" />
                                            <?php $__errorArgs = ['price_before.*'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                                <div class="invalid-feedback"><?php echo e($message); ?></div>
                                            <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                        </div>
                                        <div class="mb-4 col-md-3">
                                            <label class="text-uppercase form-sub-label" for="date">Date</label>
                                            <input class="form-control" id="date" type="date"
                                                name="early_bird_specials_date"
                                                value="<?php echo e(old('early_bird_specials_date')); ?>" />
                                            <?php $__errorArgs = ['early_bird_specials_date'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                                <div class="invalid-feedback"><?php echo e($message); ?></div>
                                            <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                        </div>
                                        <div class="mb-4 col-md-3">
                                            <label class="text-uppercase form-sub-label" for="priceOnorAfter">Price On or
                                                After</label>
                                            <input class="form-control" id="priceOnorAfter" type="number"
                                                step="0.01" name="price_on_or_after[]"
                                                value="<?php echo e(old('price_on_or_after.0')); ?>" />
                                            <?php $__errorArgs = ['price_on_or_after.*'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                                <div class="invalid-feedback"><?php echo e($message); ?></div>
                                            <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                        </div>
                                    </div>
                                    <div class="row align-items-center">
                                        <div class="mb-4 col-md">
                                            <label class="text-uppercase visually-hidden form-label"
                                                for="cost">From</label>
                                            <input class="form-control" id="cost" type="number" step="0.01"
                                                name="early_bird_from[]" value="<?php echo e(old('early_bird_from.1')); ?>" />
                                        </div>
                                        <div class="mb-4 col-md">
                                            <label class="text-uppercase visually-hidden form-label"
                                                for="to">to</label>
                                            <input class="form-control" id="to" type="number" step="0.01"
                                                name="early_bird_to[]" value="<?php echo e(old('early_bird_to.1')); ?>" />
                                        </div>
                                        <div class="mb-4 col-md-3">
                                            <label class="text-uppercase visually-hidden form-label"
                                                for="priceBefore">Price
                                                Before</label>
                                            <input class="form-control" id="priceBefore" type="number" step="0.01"
                                                name="price_before[]" value="<?php echo e(old('price_before.1')); ?>" />
                                        </div>
                                        <div class="col-md-3"></div>
                                        <div class="mb-4 col-md-3">
                                            <label class="text-uppercase visually-hidden form-label" for="priceOnorAfter">
                                                Price On
                                                or After</label>
                                            <input class="form-control" id="price_on_or_after[]" type="number"
                                                step="0.01" <?php $__errorArgs = ['price_on_or_after.1'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                                name="price_on_or_after[]" value="<?php echo e(old('price_on_or_after.1')); ?>" />
                                        </div>
                                    </div>
                                    <div class="row align-items-center">
                                        <div class="mb-4 col-md">
                                            <label class="text-uppercase visually-hidden form-label"
                                                for="cost">From</label>
                                            <input class="form-control" id="cost" type="number" step="0.01"
                                                name="early_bird_from[]" value="<?php echo e(old('early_bird_from.2')); ?>" />
                                            <?php $__errorArgs = ['early_bird_to.*'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                                <div class="invalid-feedback"><?php echo e($message); ?></div>
                                            <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                        </div>
                                        <div class="mb-4 col-md">
                                            <label class="text-uppercase visually-hidden form-label"
                                                for="to">to</label>
                                            <input class="form-control" id="to" type="number" step="0.01"
                                                name="early_bird_to[]" value="<?php echo e(old('early_bird_to.2')); ?>" />
                                        </div>
                                        <div class="mb-4 col-md-3">
                                            <label class="text-uppercase visually-hidden form-label"
                                                for="priceBefore">Price
                                                Before</label>
                                            <input class="form-control" id="priceBefore" type="number" step="0.01"
                                                name="price_before[]" value="<?php echo e(old('price_before.2')); ?>" />
                                        </div>
                                        <div class="col-md-3"></div>
                                        <div class="mb-4 col-md-3">
                                            <label class="text-uppercase visually-hidden form-label"
                                                for="priceOnorAfter">Price On
                                                or After</label>
                                            <input class="form-control" id="priceOnorAfter" type="number"
                                                step="0.01" name="price_on_or_after[]"
                                                value="<?php echo e(old('price_on_or_after.2')); ?>" />
                                        </div>
                                    </div>
                                    <div class="row align-items-center">
                                        <div class="mb-4 col-md">
                                            <label class="text-uppercase visually-hidden form-label"
                                                for="cost">From</label>
                                            <input class="form-control" id="cost" type="number" step="0.01"
                                                name="early_bird_from[]" value="<?php echo e(old('early_bird_from.3')); ?>" />
                                        </div>
                                        <div class="mb-4 col-md">
                                            <label class="text-uppercase visually-hidden form-label"
                                                for="to">to</label>
                                            <input class="form-control" id="to" type="number" step="0.01"
                                                name="early_bird_to[]" value="<?php echo e(old('early_bird_to.3')); ?>" />
                                        </div>
                                        <div class="mb-4 col-md-3">
                                            <label class="text-uppercase visually-hidden form-label"
                                                for="priceBefore">Price
                                                Before</label>
                                            <input class="form-control" id="priceBefore" type="number" step="0.01"
                                                name="price_before[]" value="<?php echo e(old('price_before.3')); ?>" />
                                        </div>
                                        <div class="col-md-3"></div>
                                        <div class="mb-4 col-md-3">
                                            <label class="text-uppercase visually-hidden form-label"
                                                for="priceOnorAfter">Price On
                                                or After</label>
                                            <input class="form-control" id="priceOnorAfter" type="number"
                                                step="0.01" name="price_on_or_after[]"
                                                value="<?php echo e(old('price_on_or_after.3')); ?>" />
                                        </div>
                                    </div>
                                </div>
                            <?php else: ?>
                                <!-- Early Bird Pricing -->
                                <div class="col-md-12 mt-4 mb-4 to_set_mb3">
                                    <div class="form-label text-uppercase">Early Bird Pricing</div>
                                    <?php for($index = 0; $index < 4; $index++): ?>
                                        <div class="row align-items-center">
                                            <div class="mb-4 col-md">
                                                <?php if($index == 0): ?>
                                                    <label class="text-uppercase form-sub-label"
                                                        for="early_bird_from_<?php echo e($index); ?>">From</label>
                                                <?php endif; ?>
                                                <input class="form-control" id="early_bird_from_<?php echo e($index); ?>"
                                                    type="number" step="0.01" name="early_bird_from[]"
                                                    value="<?php echo e(old('early_bird_from.' . $index, $earlyBirdPricing[$index]->from ?? '')); ?>" />
                                            </div>
                                            <div class="mb-4 col-md">
                                                <?php if($index == 0): ?>
                                                    <label class="text-uppercase form-sub-label"
                                                        for="early_bird_to_<?php echo e($index); ?>">To</label>
                                                <?php endif; ?>
                                                <input class="form-control" id="early_bird_to_<?php echo e($index); ?>"
                                                    type="number" step="0.01" name="early_bird_to[]"
                                                    value="<?php echo e(old('early_bird_to.' . $index, $earlyBirdPricing[$index]->to ?? '')); ?>" />
                                            </div>
                                            <div class="mb-4 col-md-3">
                                                <?php if($index == 0): ?>
                                                    <label class="text-uppercase form-sub-label"
                                                        for="price_before_<?php echo e($index); ?>">Price Before</label>
                                                <?php endif; ?>
                                                <input class="form-control" id="price_before_<?php echo e($index); ?>"
                                                    type="number" step="0.01" name="price_before[]"
                                                    value="<?php echo e(old('price_before.' . $index, $earlyBirdPricing[$index]->price_before ?? '')); ?>" />
                                            </div>
                                            <?php if($index == 0): ?>
                                                <div class="mb-4 col-md-3">
                                                    <label class="text-uppercase form-sub-label"
                                                        for="early_bird_specials_date">Date</label>
                                                    <input class="form-control" id="early_bird_specials_date"
                                                        type="date" name="early_bird_specials_date"
                                                        value="<?php echo e(old('early_bird_specials_date', $program->early_bird_specials_date)); ?>" />
                                                </div>
                                            <?php else: ?>
                                                <div class="mb-4 col-md-3"></div>
                                            <?php endif; ?>

                                            <div class="mb-4 col-md-3">
                                                <?php if($index == 0): ?>
                                                    <label class="text-uppercase form-sub-label"
                                                        for="price_on_or_after_<?php echo e($index); ?>">Price On or
                                                        After</label>
                                                <?php endif; ?>
                                                <input class="form-control" id="price_on_or_after_<?php echo e($index); ?>"
                                                    type="number" step="0.01" name="price_on_or_after[]"
                                                    value="<?php echo e(old('price_on_or_after.' . $index, $earlyBirdPricing[$index]->price_on_or_after ?? '')); ?>" />
                                            </div>
                                        </div>
                                    <?php endfor; ?>
                                </div>

                            <?php endif; ?>
                        </div>

                        <!-- Program Description -->
                        <div class="mb-4 col-md-12">
                            <label class="form-label text-uppercase" for="program_description">Program Description
                                <span class="red-text">*</span></label>
                            <textarea class="form-control <?php $__errorArgs = ['program_description'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" id="program_description"
                                name="program_description" style="height: 140px">
                            <?php echo e(old('program_description', $program->program_description)); ?>

                        </textarea>
                            <?php $__errorArgs = ['program_description'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                <div class="invalid-feedback"><?php echo e($message); ?></div>
                            <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                        </div>

                        <!-- Payment -->
                        <div class="col-12 mt-4">
                            <div class="form-label text-uppercase">Payment</div>
                            <div class="row">
                                <div class="mb-4 col-md">
                                    <div class="form-check col-md-">
                                        <input class="form-check-input" id="FullPaymentOnly" type="radio"
                                            value="full" name="payment"
                                            <?php echo e(old('payment', $program->payment) === 'full' ? 'checked' : ''); ?> />
                                        <label class="form-check-label p text-uppercase" for="FullPaymentOnly">Full
                                            Payment only</label>
                                    </div>
                                </div>
                                <div class="mb-4 col-md">
                                    <div class="form-check col-md-">
                                        <input class="form-check-input" id="recurringPayment" type="radio"
                                            value="recurring" name="payment"
                                            <?php echo e(old('payment', $program->payment) === 'recurring' ? 'checked' : ''); ?> />
                                        <label class="form-check-label p text-uppercase" for="RecurringPayment">Recurring
                                            Payment</label>
                                    </div>
                                </div>
                                <div class="mb-4 col-md">
                                    <div class="form-check col-md-">
                                        <input class="form-check-input" id="SplitPayment" type="radio" value="split"
                                            name="payment"
                                            <?php echo e(old('payment', $program->payment) === 'split' ? 'checked' : ''); ?> />
                                        <label class="form-check-label p text-uppercase" for="SplitPayment">Split
                                            Payment</label>
                                    </div>
                                </div>
                            </div>
                        </div>


                        <div class="mb-4" id="minimumMonthlyPaymentDiv"
                            style="<?php echo e(old('payment', $program->payment) === 'recurring' ? 'display:block;' : 'display:none;'); ?>">
                            <label class="text-uppercase form-label" for="minimumMonthlyPayment">
                                Minimum Monthly Payment
                            </label>
                            <input class="form-control <?php $__errorArgs = ['minimumMonthlyPayment'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>"
                                id="minimumMonthlyPayment" name="minimumMonthlyPayment" type="number" step="0.01"
                                min="0" placeholder="Enter amount in dollars"
                                value="<?php echo e(old('minimumMonthlyPayment', $program->minimum_recurring_amount)); ?>" />
                            <?php $__errorArgs = ['minimumMonthlyPayment'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                <div class="invalid-feedback"><?php echo e($message); ?></div>
                            <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                        </div>

                        <div class="mb-4" id="status">
                            <label class="form-label text-uppercase" for="status">Status</label>
                            <div class="select-arrow position-relative">
                                <select class="form-control <?php $__errorArgs = ['status'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" id="status"
                                    name="status">
                                    <option value="" <?php echo e(old('status', $program->status) == '' ? 'selected' : ''); ?>>
                                        Select
                                    </option>
                                    <option value="draft"
                                        <?php echo e(old('status', $program->status) == 'draft' ? 'selected' : ''); ?>>Draft
                                    </option>
                                    <option value="public"
                                        <?php echo e(old('status', $program->status) == 'public' ? 'selected' : ''); ?>>Public
                                    </option>
                                    <option value="private"
                                        <?php echo e(old('status', $program->status) == 'private' ? 'selected' : ''); ?>>Private
                                    </option>
                                </select>
                                <span class="arrow"><i class="bi bi-chevron-down"></i></span>
                                <?php $__errorArgs = ['status'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                    <div class="invalid-feedback"><?php echo e($message); ?></div>
                                <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                            </div>
                        </div>


                        <div class="d-flex justify-content-between align-items-center mt-5">
                            <div class="col-md-6 d-flex justify-content-end mt-5 mr-1">
                                <button class="cta py-0 mr-1" style="margin-right:1rem">Update Program <span
                                        class="d-none ms-3 cta-response"></span></button>
                            </div>
                    </form>
                    <div class="col-md-6 d-flex mt-5">
                        <form action="<?php echo e(route('admin.program.destroy', ['program' => $program->slug])); ?>"
                            method="POST">
                            <?php echo csrf_field(); ?>
                            <?php echo method_field('DELETE'); ?>
                            <button type="submit" class="cta py-0">Delete Program</button>
                        </form>

                    </div>
                </div>
            </div>
        </div>
        </div>
        <script src="<?php echo e(asset('tinymce/tinymce.min.js')); ?>"></script>

        <script>
            document.addEventListener('DOMContentLoaded', function() {
                const earlyBirdCheckbox = document.getElementById('enable_early_bird_specials');
                const earlyBirdPricingSection = document.getElementById('early_bird_pricing_section');

                function toggleEarlyBirdPricing() {
                    if (earlyBirdCheckbox.checked) {
                        earlyBirdPricingSection.style.display = '';
                    } else {
                        earlyBirdPricingSection.style.display = 'none';
                    }
                }


                toggleEarlyBirdPricing();


                earlyBirdCheckbox.addEventListener('change', toggleEarlyBirdPricing);


                const recurringPaymentRadio = document.getElementById('recurringPayment');
                const oneTimePaymentRadio = document.getElementById('FullPaymentOnly');
                const splitPaymentRadio = document.getElementById('SplitPayment');
                const minimumMonthlyPaymentDiv = document.getElementById('minimumMonthlyPaymentDiv');

                recurringPaymentRadio.addEventListener('change', togglePaymentDiv);
                oneTimePaymentRadio.addEventListener('change', togglePaymentDiv);
                splitPaymentRadio.addEventListener('change', togglePaymentDiv);

                function togglePaymentDiv() {
                    if (recurringPaymentRadio.checked) {
                        minimumMonthlyPaymentDiv.style.display = 'block';
                    } else {
                        minimumMonthlyPaymentDiv.style.display = 'none';
                    }
                }

                showSessionSuccessMessage();
                showSessionErrorMessage();
            });
        </script>

    </section>
    <script>
        tinymce.init({
            selector: 'textarea#program_description',
            width: 1000,
            height: 300,
            plugins: [
                'advlist', 'autolink', 'link', 'image', 'charmap', 'preview', 'anchor',
                'searchreplace', 'wordcount', 'code', 'fullscreen', 'insertdatetime',
                'table', 'print'
            ],
            toolbar: 'undo redo | bold italic underline | alignleft aligncenter alignright alignjustify | bullist numlist | link image | print fullscreen',
            menubar: false,
            content_style: 'body { font-family: Helvetica, Arial, sans-serif; font-size: 16px; }'
        });
    </script>


<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.app', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH X:\Mass-Premier-Courts\resources\views/admin/editProgram.blade.php ENDPATH**/ ?>