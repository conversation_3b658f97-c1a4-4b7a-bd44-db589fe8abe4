@extends('layouts.app')
@section('title', 'Admin')
@section('content')
    <section class="page_title text-center pt-5">

        <h1 class="text-uppercase lh-1 mb-0">Users</h1>
        @if (session('success'))
            <div id="successMessageForSession">
                <span id="successText">{{ session('success') }}</span>
            </div>
        @endif

        @if (session('error'))
            <div id="errorMessageForSession">
                <span id="errorText">{{ session('error') }}</span>
            </div>
        @endif


    </section>
    <section class="sec admin-welcome">
        <div class="container">
            <div class="heading align-items-center d-flex flex-column text-center mb-5">
                <span class="hero-bar d-inline-flex"></span>
            </div>
            <div class="row admin-ctas justify-content-center">
                <div class="col-lg-10">
                    <div class="row justify-content-center">
                        <div class="col-md-6 mt-4">
                            <button
                                class="cta-admin btn-link d-flex text-center align-items-center justify-content-center text-uppercase text-decoration-none w-100 border-0"
                                id="add-the-player">Add Player</button>
                        </div>
                        <div class="col-md-6 mt-4">
                            <button
                                class="cta-admin btn-link d-flex text-center align-items-center justify-content-center text-uppercase text-decoration-none w-100 border-0"
                                id="add-the-admin">Add Admin Account</button>
                        </div>
                    </div>
                    <div class="row justify-content-center">
                        <div class="col-md-6 mt-4">
                            <button
                                class="cta-admin btn-link d-flex text-center align-items-center justify-content-center text-uppercase text-decoration-none w-100 border-0"
                                id="add-the-coach">Add Coach</button>
                        </div>
                        <div class="col-md-6 mt-4">
                            <button
                                class="cta-admin btn-link d-flex text-center align-items-center justify-content-center text-uppercase text-decoration-none w-100 border-0"
                                id="add-the-guardian">Add Guardian</button>
                        </div>
                    </div>
                    {{-- <div class="row justify-content-center">
                        <div class="col-md-6 mt-4">
                            <button
                                class="cta-admin btn-link d-flex text-center align-items-center justify-content-center text-uppercase text-decoration-none w-100 border-0"
                                id="add-the-team">Create Team</button>
                        </div>
                    </div> --}}
                </div>
            </div>
        </div>
    </section>

    <section class="sec partition-hr">
        <a class="cta mb-5 ms-5" href="{{ route('admin.dashboard') }}">Back</a>
        <div class="container">
            <hr class="border-navy opacity-100 my-0" />
        </div>
    </section>
    <section class="sec program-table">


        @livewire('admin.all-users')




        {{-- <div class="container">
            <div class="heading text-center mb-5">
                <h2 class="text-uppercase fs-6 mb-0">All Users</h2>
            </div>
            <form class="form row table-filter justify-content-center" id="filterForm" method="get"
                action="{{ route('admin.users') }}">
                @csrf
                <div class="col-md-auto">
                    <div class="filter-option">
                        <div class="mb-4">
                            <label class="form-label text-uppercase" for="filter">Filter By</label>
                            <div class="select-arrow position-relative">
                                <select class="form-control" id="filter" name="filter">
                                    <option value="">All</option>
                                    <option value="Town" {{ request('filter') === 'Town' ? 'selected' : '' }}>
                                        Town</option>
                                    <option value="Age" {{ request('filter') === 'Age' ? 'selected' : '' }}>
                                        Age</option>
                                    <option value="Grade" {{ request('filter') === 'Grade' ? 'selected' : '' }}>
                                        Grade</option>
                                </select>
                                <span class="arrow"><i class="bi bi-chevron-down"></i></span>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-auto">
                    <div class="filter-option">
                        <div class="mb-4">
                            <label class="form-label text-uppercase" for="search">Search</label>
                            <div class="flex items-center">
                                <input class="form-control" id="search" type="text" name="search"
                                    value="{{ request('search') }}" />
                            </div>
                        </div>
                    </div>
                </div>
            </form>
            <div>
                <div class="col-md-auto" id="sendEmailButton">
                    <button class="cta"> Send Email</button>
                </div>
            </div>


            <div class="program-table">
                <div class="table-program table-responsive">

                    <table class="table table-hover" width="100%" id="user-table">
                        <thead>
                            <tr>
                                <th class="py-4">First Name</th>
                                <th class="py-4">Last Name</th>
                                <th class="py-4">User Type</th>
                                <th class="py-4">Email</th>
                                <th class="py-4">Town</th>
                                <th class="py-4">Age</th>
                                <th class="py-4">Grade</th>
                                <th class="py-4" width="20"></th>
                                <th class="py-4" width="20"></th>
                            </tr>
                        </thead>
                        <tbody>
                            @foreach ($users as $user)
                                <tr>

                                    <td class="py-4" valign="middle">{{ $user->firstName }}</td>
                                    <td class="py-4" valign="middle">{{ $user->lastName }}</td>
                                    <td class="py-4" valign="middle">
                                        @foreach ($user->roles as $role)
                                            {{ $role->name }}@if (!$loop->last)
                                                ,
                                            @endif
                                        @endforeach
                                    </td>

                                    <td class="py-4" valign="middle">{{ $user->email ?? 'N/A' }}</td>
                                    <td class="py-4" valign="middle">{{ $user->town ?? 'N/A' }}
                                    </td>
                                    <td class="py-4" valign="middle">{{ $user->age ?? 'N/A' }}
                                    </td>
                                    <td class="py-4" valign="middle">{{ $user->grade ?? 'N/A' }}
                                    </td>
                                    @if ($user->hasRole('admin'))
                                        <td class="py-4" valign="middle">
                                            <a href="" class="action edit" id="edit-admin"
                                                data-admin-id="{{ $user->id }}">
                                                <img src="{{ asset('images/edit-icon.svg') }}" alt="Edit"
                                                    width="20" height="20" />
                                            </a>
                                        </td>
                                    @elseif($user->hasRole('coach'))
                                        <td class="py-4" valign="middle">
                                            <a href="" class="action edit" id="edit-coach"
                                                data-coach-id="{{ $user->id }}">
                                                <img src="{{ asset('images/edit-icon.svg') }}" alt="Edit"
                                                    width="20" height="20" />
                                            </a>
                                        </td>
                                    @elseif($user->hasRole('guardian'))
                                        <td class="py-4" valign="middle">
                                            <a href="" id="edit-guardian" data-guardian-id="{{ $user->id }}"
                                                class="action edit">
                                                <img src="{{ asset('images/edit-icon.svg') }}" alt="Edit"
                                                    width="20" height="20" />
                                            </a>
                                        </td>
                                    @elseif($user->hasRole('player'))
                                        <td class="py-4" valign="middle">
                                            <a href="" class="action edit" id="edit-player"
                                                data-player-id="{{ $user->id }}">
                                                <img src="{{ asset('images/edit-icon.svg') }}" alt="Edit"
                                                    width="20" height="20" />
                                            </a>
                                        </td>
                                    @endif

                                    <td class="py-4" valign="middle">
                                        <form id="deleteUser-{{ $user->id }}"
                                            onsubmit="showConfirmation(event, 'deleteUser-{{ $user->id }}')"
                                            action="{{ route('admin.destroy', ['user' => $user->id]) }}" method="POST"
                                            class="inline-form mb-0">
                                            @csrf
                                            @method('DELETE')
                                            <button type="submit" class="action edit bg-transparent border-0 p-0">
                                                <img src="{{ asset('images/delete-icon.svg') }}" alt="Delete"
                                                    width="18" height="20" />
                                            </button>
                                        </form>
                                    </td>
                                </tr>
                            @endforeach
                        </tbody>
                    </table>
                    <div class="mt-4 d-flex justify-content-center pagination">
                        {{ $users->onEachSide(1)->links('pagination::bootstrap-5') }}
                    </div>
                </div>
            </div>
        </div> --}}




        <!--Add Player modal in Admin -->

        <div class="modal fade" id="player-add-modal" tabindex="-1" aria-labelledby="playerAddLabel" aria-hidden="true"
            data-bs-backdrop="static">
            <div class="modal-dialog modal-dialog-centered" style="max-width: 1160px">
                <div class="modal-content">
                    <div class="modal-body p-5">
                        <span class="modal-close" data-bs-dismiss="modal" id="cancel-player-add">&#x2715;</span>
                        <form class="form row" id="submit-Player-Data" enctype="multipart/form-data">
                            <div id="successMessage" class="alert alert-success d-none" role="alert"></div>
                            <div id="errorMessage" class="alert alert-danger d-none" role="alert"></div>
                            @csrf
                            <div class="mb-4 col-md-6">
                                <label class="form-label text-uppercase" for="player-FirstName">Player First Name</label>
                                <input class="form-control" id="player-FirstName" type="text" name="firstName" />
                                <div class="invalid-feedback-admin d-none text-danger" id="firstName-Error"></div>
                            </div>
                            <div class="mb-4 col-md-6">
                                <label class="form-label text-uppercase" for="playerLastName">Player Last Name</label>
                                <input class="form-control" id="playerLastName" type="text" name="lastName" />
                                <div class="invalid-feedback-admin d-none text-danger" id="lastName-Error"></div>
                            </div>
                            <div class="mb-4 col-md-6">
                                <label class="form-label text-uppercase" for="Email">Email(optional)</label>
                                <input class="form-control" id="Email" type="text" name="email" />
                                <div class="invalid-feedback-admin d-none text-danger" id="email-Error"></div>
                            </div>

                            <div class="mb-4 col-md-6">
                                <label class="form-label text-uppercase" for="playerGender">Gender</label>
                                <div class="select-arrow position-relative">
                                    <select class="form-control" name="gender" id="playerGender">
                                        <option value="">Select Gender</option>
                                        <option value="boy">Boy</option>
                                        <option value="girl">Girl</option>
                                    </select>

                                    <span class="arrow"><i class="bi bi-chevron-down"></i></span>

                                </div>
                                <div class="invalid-feedback d-none" id="gender-Error"></div>
                            </div>



                            <div class="mb-4 col-md-6">
                                <label class="form-label text-uppercase" for="birth-date">Birth date</label>
                                <input class="form-control" id="birth-date" type="date" name="birthDate" />
                                <div class="invalid-feedback-admin d-none text-danger" id="birthDate-Error"></div>
                            </div>
                            <div class="mb-4 col-md-6">
                                <label class="form-label text-uppercase" for="grade-EnteringintheFall">Grade (Entering in
                                    the
                                    Fall)</label>
                                <input class="form-control" id="grade-EnteringintheFall" type="text"
                                    name="grade" />
                                <div class="invalid-feedback-admin d-none text-danger" id="grade-Error"></div>
                            </div>
                            <div class="mb-4 col-md-12">
                                <label class="form-label text-uppercase" for="guardianSelect">SELECT GUARDIAN</label>
                                <select class="form-control" id="guardianSelect" name="guardian-name"></select>
                                <div class="invalid-feedback-admin d-none text-danger" id="guardian_id-Error"></div>
                            </div>


                            <div class="mb-4 col-md-6">
                                <label class="form-label text-uppercase" for="street">Street</label>
                                <input class="form-control" id="street" type="text" name="street" />
                                <div class="invalid-feedback-admin d-none text-danger" id="street-Error"></div>
                            </div>
                            <div class="mb-4 col-md-6">
                                <label class="form-label text-uppercase" for="town">Town</label>
                                <input class="form-control" id="town" type="text" name="town" />
                                <div class="invalid-feedback-admin d-none text-danger" id="town-Error"></div>
                            </div>
                            <div class="mb-4 col-md-12">
                                <label class="form-label text-uppercase" for="state">State</label>
                                <input class="form-control" id="state" type="text" name="state" />
                                <div class="invalid-feedback-admin d-none text-danger" id="state-Error"></div>
                            </div>

                            <div class="col-md-6">
                                <div class="upload-pic d-flex align-items-center">
                                    <div class="form-label text-uppercase me-4">Profile Photo (optional)</div>
                                    <div class="icon-upload" style="cursor: pointer">
                                        <img src="{{ asset('images/upload-icon.svg') }}" alt="Upload Icon" width="40"
                                            height="50" id="uploadIcon" />
                                    </div>
                                    <input type="file" id="profilePhoto" name="profilePhoto" accept="image/*"
                                        style="display: none;" />
                                    <div class="invalid-feedback text-danger" id="profilePhotoError"></div>
                                </div>
                                <div class="file-name mt-2" id="selectedFileName">No file selected</div>
                                <img id="imagePreview" src="" alt="Image Preview" class="mt-2"
                                    style="max-width: 100px; max-height: 100px; display: none;" />
                            </div>
                            <div class="col-md-6 d-flex align-items-center justify-content-end" style="min-height: 40px">
                                <button class="cta" id="add-player-button">Add Player</button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>





        <!-- Edit Player Modal -->
        <div class="modal fade" id="player-edit-modal" tabindex="-1" aria-labelledby="playerEditLabel"
            aria-hidden="true" data-bs-backdrop="static">
            <div class="modal-dialog modal-dialog-centered" style="max-width: 1160px">
                <div class="modal-content">
                    <div class="modal-body p-5">
                        <span class="modal-close" data-bs-dismiss="modal" id="cancel-player-edit">&#x2715;</span>
                        <form class="form row" id="update-Player-Data" enctype="multipart/form-data">
                            <div id="editSuccessMessage" class="alert alert-success d-none" role="alert"></div>
                            <div id="editErrorMessage" class="alert alert-danger d-none" role="alert"></div>
                            @csrf

                            <input type="hidden" id="playerId" name="playerId" />

                            <div class="mb-4 col-md-6">
                                <label class="form-label text-uppercase" for="editPlayerFirstName">Player First
                                    Name</label>
                                <input class="form-control" id="editPlayerFirstName" type="text" name="firstName" />
                                <div class="invalid-feedback-player d-none text-danger" id="editFirstName-Error"></div>
                            </div>
                            <div class="mb-4 col-md-6">
                                <label class="form-label text-uppercase" for="editPlayerLastName">Player Last Name</label>
                                <input class="form-control" id="editPlayerLastName" type="text" name="lastName" />
                                <div class="invalid-feedback-player d-none text-danger" id="editLastName-Error"></div>
                            </div>
                            <div class="mb-4 col-md-6">
                                <label class="form-label text-uppercase" for="editEmail">Email(optional)</label>
                                <input class="form-control" id="editEmail" type="text" name="email" />
                                <div class="invalid-feedback-player d-none text-danger" id="editEmail-Error"></div>
                            </div>

                            <div class="mb-4 col-md-6">
                                <label class="form-label text-uppercase" for="editGender">Gender</label>
                                <div class="select-arrow position-relative">
                                    <select class="form-control" name="gender" id="editGender">
                                        <option value="">Select Gender</option>
                                        <option value="boy">Boy</option>
                                        <option value="girl">Girl</option>
                                    </select>

                                    <span class="arrow"><i class="bi bi-chevron-down"></i></span>

                                </div>
                                <div class="invalid-feedback d-none" id="editGender-Error"></div>
                            </div>






                            <div class="mb-4 col-md-6">
                                <label class="form-label text-uppercase" for="editBirthDate">Birth Date</label>
                                <input class="form-control" id="editBirthDate" type="date" name="birthDate" />
                                <div class="invalid-feedback-player d-none text-danger" id="editBirthDate-Error"></div>
                            </div>
                            <div class="mb-4 col-md-6">
                                <label class="form-label text-uppercase" for="editGrade">Grade (Entering in the
                                    Fall)</label>
                                <input class="form-control" id="editGrade" type="text" name="grade" />
                                <div class="invalid-feedback-player d-none text-danger" id="editGrade-Error"></div>
                            </div>
                            <div class="mb-4 col-md-12">
                                <label class="form-label text-uppercase" for="editGuardianSelect">SELECT GUARDIAN</label>
                                <select class="form-control" id="editGuardianSelect" name="guardianId"></select>
                                <div class="invalid-feedback-player d-none text-danger" id="editGuardianId-Error"></div>
                            </div>
                            <div class="mb-4 col-md-6">
                                <label class="form-label text-uppercase" for="editStreet">Street</label>
                                <input class="form-control" id="editStreet" type="text" name="street" />
                                <div class="invalid-feedback-player d-none text-danger" id="editStreet-Error"></div>
                            </div>
                            <div class="mb-4 col-md-6">
                                <label class="form-label text-uppercase" for="editTown">Town</label>
                                <input class="form-control" id="editTown" type="text" name="town" />
                                <div class="invalid-feedback-player d-none text-danger" id="editTown-Error"></div>
                            </div>
                            <div class="mb-4 col-md-12">
                                <label class="form-label text-uppercase" for="editState">State</label>
                                <input class="form-control" id="editState" type="text" name="state" />
                                <div class="invalid-feedback-player d-none text-danger" id="editState-Error"></div>
                            </div>

                            <div class="col-md-6">
                                <div class="upload-pic d-flex align-items-center">
                                    <div class="form-label text-uppercase me-4">Profile Photo (optional)</div>
                                    <div class="icon-upload" style="cursor: pointer">
                                        <img src="{{ asset('images/upload-icon.svg') }}" alt="Upload Icon" width="40"
                                            height="50" id="edit-upload-Icon" />
                                    </div>
                                    <input type="file" id="editProfilePhoto" name="profilePhoto" accept="image/*"
                                        style="display: none;" />
                                    <div class="invalid-feedback-player d-none text-danger" id="editProfilePhoto-Error">
                                    </div>
                                </div>
                                <div class="file-name mt-2" id="editSelectedFileName">No file selected</div>
                                <img id="editImagePreview" src="" alt="Image Preview" class="mt-2"
                                    style="max-width: 100px; max-height: 100px; display: none;" />
                            </div>
                            <div class="col-md-6 d-flex align-items-center justify-content-end" style="min-height: 40px">
                                <button class="cta" id="update-player-button">Update Player</button>
                            </div>

                            <!-- Background Overlay for Blur Effect -->
                            <div id="blurOverlay" class="d-none"
                                style="position: fixed; top: 0; left: 0; width: 100%; height: 100%;
            background: rgba(0, 0, 0, 0.3); backdrop-filter: blur(5px); z-index: 1049;">
                            </div>

                            <!-- Player Update Message -->
                            <div id="playerUpdateMessage" class="alert text-center d-none"
                                style="position: absolute; top: 50%; left: 50%; transform: translate(-50%, -50%);
            width: 350px; height: 120px; z-index: 1050;
            display: flex; align-items: center; justify-content: center;
            box-shadow: 0 0 15px rgba(0, 0, 0, 0.3);">
                            </div>



                        </form>
                    </div>
                </div>
            </div>
        </div>










        <!--ADD admin Modal -->

        <div class="modal fade" id="admin-add-modal" tabindex="-1" aria-labelledby="adminAddLabel" aria-hidden="true"
            data-bs-backdrop="static">
            <div class="modal-dialog modal-dialog-centered" style="max-width: 1160px">
                <div class="modal-content">
                    <div class="modal-body p-5">
                        <span class="modal-close" data-bs-dismiss="modal" id="cancel-admin-add">&#x2715;</span>
                        <form class="form row" id="submit-Admin-Data">
                            <div id="adminSuccessMessage" class="alert alert-success d-none" role="alert"></div>
                            <div id="adminErrorMessage" class="alert alert-danger d-none" role="alert"></div>
                            @csrf
                            <div class="mb-4 col-md-6">
                                <label class="form-label text-uppercase" for="admin-FirstName">Admin First Name</label>
                                <input class="form-control" id="admin-FirstName" type="text" name="firstName" />
                                <div class="invalid-feedback-admin d-none text-danger" id="adminFirstName-Error"></div>
                            </div>
                            <div class="mb-4 col-md-6">
                                <label class="form-label text-uppercase" for="adminLastName">Admin Last Name</label>
                                <input class="form-control" id="adminLastName" type="text" name="lastName" />
                                <div class="invalid-feedback-admin d-none text-danger" id="adminLastName-Error"></div>
                            </div>
                            <div class="mb-4 col-md-6">
                                <label class="form-label text-uppercase" for="adminEmail">Email</label>
                                <input class="form-control" id="adminEmail" type="email" name="email" />
                                <div class="invalid-feedback-admin d-none text-danger" id="adminEmail-Error"></div>
                            </div>
                            <div class="mb-4 col-md-6">
                                <label class="form-label text-uppercase" for="adminPassword">Password</label>
                                <input class="form-control" id="adminPassword" type="password" name="password" />
                                <div class="invalid-feedback-admin d-none text-danger" id="adminPassword-Error"></div>
                            </div>
                            <div class="col-md-12 d-flex align-items-center justify-content-end" style="min-height: 40px">
                                <button class="cta" id="add-admin-button">Add Admin</button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>




        <!-- Team Creation Modal -->
        {{-- <div class="modal fade" id="team-add-modal" tabindex="-1" aria-labelledby="teamAddLabel" aria-hidden="true"
            data-bs-backdrop="static">
            <div class="modal-dialog modal-dialog-centered" style="max-width: 1160px">
                <div class="modal-content">
                    <div class="modal-body p-5">
                        <span class="modal-close" data-bs-dismiss="modal" id="cancel-team-add">&#x2715;</span>
                        <form class="form row" id="submit-Team-Data">
                            <div id="teamSuccessMessage" class="alert alert-success d-none" role="alert"></div>
                            <div id="teamErrorMessage" class="alert alert-danger d-none" role="alert"></div>
                            @csrf
                            <div class="mb-4 col-md-12">
                                <label class="form-label text-uppercase" for="team-name">Team Name</label>
                                <input class="form-control" id="team-name" type="text" name="teamName" />
                                <div class="invalid-feedback-admin d-none text-danger" id="teamName-Error"></div>
                            </div>

                            <div class="mb-4 col-md-12">
                                <label class="form-label text-uppercase" for="coachSelect">SELECT COACH</label>
                                <select class="form-control select2" id="coachSelect" name="coach_id">
                                    <option value="">Select a Coach</option>
                                </select>
                                <div class="invalid-feedback-admin d-none text-danger" id="coach_id-Error"></div>
                            </div>

                            <div class="mb-4 col-md-12">
                                <label class="form-label text-uppercase" for="programSelect">SELECT PROGRAM</label>
                                <select class="form-control select2" id="programSelect" name="program_id">
                                    <option value="">Select a Program</option>
                                </select>
                                <div class="invalid-feedback-admin d-none text-danger" id="program_id-Error"></div>
                            </div>

                            <div class="col-md-12 d-flex align-items-center justify-content-end" style="min-height: 40px">
                                <button class="cta" id="create-team-button">Create Team</button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div> --}}

        <!-- Edit Admin Modal -->
        <div class="modal fade" id="admin-edit-modal" tabindex="-1" aria-labelledby="adminEditLabel"
            aria-hidden="true" data-bs-backdrop="static">
            <div class="modal-dialog modal-dialog-centered" style="max-width: 1160px">
                <div class="modal-content">
                    <div class="modal-body p-5">
                        <span class="modal-close" data-bs-dismiss="modal" id="cancel-admin-edit">&#x2715;</span>
                        <form class="form row" id="update-Admin-Data">
                            <div id="adminEditSuccessMessage" class="alert alert-success d-none" role="alert"></div>
                            <div id="adminEditErrorMessage" class="alert alert-danger d-none" role="alert"></div>
                            @csrf

                            <input type="hidden" id="editAdminId" name="adminId" />

                            <div class="mb-4 col-md-6">
                                <label class="form-label text-uppercase" for="editAdminFirstName">Admin First Name</label>
                                <input class="form-control" id="editAdminFirstName" type="text" name="firstName" />
                                <div class="invalid-feedback-admin d-none text-danger" id="editAdminFirstName-Error">
                                </div>
                            </div>
                            <div class="mb-4 col-md-6">
                                <label class="form-label text-uppercase" for="editAdminLastName">Admin Last Name</label>
                                <input class="form-control" id="editAdminLastName" type="text" name="lastName" />
                                <div class="invalid-feedback-admin d-none text-danger" id="editAdminLastName-Error"></div>
                            </div>
                            <div class="mb-4 col-md-6">
                                <label class="form-label text-uppercase" for="editAdminEmail">Email</label>
                                <input class="form-control" id="editAdminEmail" type="email" name="email" />
                                <div class="invalid-feedback-admin d-none text-danger" id="editAdminEmail-Error"></div>
                            </div>
                            <div class="mb-4 col-md-6">
                                <label class="form-label text-uppercase" for="editAdminPassword">Password</label>
                                <input class="form-control" id="editAdminPassword" type="password" name="password" />
                                <small class="form-text text-muted">Leave Blank to keep old Password</small>
                                <div class="invalid-feedback-admin d-none text-danger" id="editAdminPassword-Error"></div>
                            </div>
                            <div class="col-md-12 d-flex align-items-center justify-content-end" style="min-height: 40px">
                                <button class="cta" id="update-admin-button">Update Admin</button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>













        <!--Add Coach Modal -->

        <div class="modal fade" id="coach-add-modal" tabindex="-1" aria-labelledby="coachAddLabel" aria-hidden="true"
            data-bs-backdrop="static">
            <div class="modal-dialog modal-dialog-centered" style="max-width: 1160px">
                <div class="modal-content">
                    <div class="modal-body p-5">
                        <span class="modal-close" data-bs-dismiss="modal" id="cancel-coach-add">&#x2715;</span>
                        <form class="form row" id="submit-Coach-Data">
                            <div id="coachSuccessMessage" class="alert alert-success d-none" role="alert"></div>
                            <div id="coachErrorMessage" class="alert alert-danger d-none" role="alert"></div>
                            @csrf
                            <div class="mb-4 col-md-6">
                                <label class="form-label text-uppercase" for="coachFirstName">Coach First Name</label>
                                <input class="form-control" id="coachFirstName" type="text" name="firstName" />
                                <div class="invalid-feedback-coach d-none text-danger" id="coachFirstName-Error"></div>
                            </div>
                            <div class="mb-4 col-md-6">
                                <label class="form-label text-uppercase" for="coachLastName">Coach Last Name</label>
                                <input class="form-control" id="coachLastName" type="text" name="lastName" />
                                <div class="invalid-feedback-coach d-none text-danger" id="coachLastName-Error"></div>
                            </div>
                            <div class="mb-4 col-md-6">
                                <label class="form-label text-uppercase" for="townOrProgram">Town/Program</label>
                                <input class="form-control" id="townOrProgram" type="text" name="townOrProgram" />
                                <div class="invalid-feedback-coach d-none text-danger" id="coachTownOrProgram-Error">
                                </div>
                            </div>
                            <div class="mb-4 col-md-6">
                                <label class="form-label text-uppercase" for="teamName">Team Name</label>
                                <input class="form-control" id="teamName" type="text" name="teamName" />
                                <div class="invalid-feedback-coach d-none text-danger" id="coachTeamName-Error"></div>
                            </div>
                            <div class="mb-4 col-md-6">
                                <label class="form-label text-uppercase" for="coachEmail">Email</label>
                                <input class="form-control" id="coachEmail" type="email" name="email" />
                                <div class="invalid-feedback-coach d-none text-danger" id="coachEmail-Error"></div>
                            </div>
                            <div class="mb-4 col-md-6">
                                <label class="form-label text-uppercase" for="coachPassword">Password</label>
                                <input class="form-control" id="coachPassword" type="password" name="password" />
                                <div class="invalid-feedback-coach d-none text-danger" id="coachPassword-Error"></div>
                            </div>
                            <div class="col-md-12 d-flex align-items-center justify-content-end" style="min-height: 40px">
                                <button class="cta" id="add-coach-button">Add Coach</button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>



        <!--Edit Coach Modal -->


        <div class="modal fade" id="coach-edit-modal" tabindex="-1" aria-labelledby="coachEditLabel"
            aria-hidden="true" data-bs-backdrop="static">
            <div class="modal-dialog modal-dialog-centered" style="max-width: 1160px">
                <div class="modal-content">
                    <div class="modal-body p-5">
                        <span class="modal-close" data-bs-dismiss="modal" id="cancel-coach-edit">&#x2715;</span>
                        <form class="form row" id="update-Coach-Data">
                            <div id="coachEditSuccessMessage" class="alert alert-success d-none" role="alert"></div>
                            <div id="coachEditErrorMessage" class="alert alert-danger d-none" role="alert"></div>
                            @csrf
                            <input type="hidden" id="coachId" name="coachId" />

                            <div class="mb-4 col-md-6">
                                <label class="form-label text-uppercase" for="editCoachFirstName">Coach First Name</label>
                                <input class="form-control" id="editCoachFirstName" type="text" name="firstName" />
                                <div class="invalid-feedback-coach d-none text-danger" id="editCoachFirstName-Error">
                                </div>
                            </div>

                            <div class="mb-4 col-md-6">
                                <label class="form-label text-uppercase" for="editCoachLastName">Coach Last Name</label>
                                <input class="form-control" id="editCoachLastName" type="text" name="lastName" />
                                <div class="invalid-feedback-coach d-none text-danger" id="editCoachLastName-Error"></div>
                            </div>

                            <div class="mb-4 col-md-6">
                                <label class="form-label text-uppercase" for="editTownOrProgram">Town/Program</label>
                                <input class="form-control" id="editTownOrProgram" type="text"
                                    name="townOrProgram" />
                                <div class="invalid-feedback-coach d-none text-danger" id="editCoachTownOrProgram-Error">
                                </div>
                            </div>

                            <div class="mb-4 col-md-6">
                                <label class="form-label text-uppercase" for="editTeamName">Team Name</label>
                                <input class="form-control" id="editTeamName" type="text" name="teamName" />
                                <div class="invalid-feedback-coach d-none text-danger" id="editCoachTeamName-Error"></div>
                            </div>

                            <div class="mb-4 col-md-6">
                                <label class="form-label text-uppercase" for="editCoachEmail">Email</label>
                                <input class="form-control" id="editCoachEmail" type="email" name="email" />
                                <div class="invalid-feedback-coach d-none text-danger" id="editCoachEmail-Error"></div>
                            </div>

                            <div class="mb-4 col-md-6">
                                <label class="form-label text-uppercase" for="editCoachPassword">Password</label>
                                <input class="form-control" id="editCoachPassword" type="password" name="password" />
                                <p class="form-text text-muted">Leave Blank to keep old Password</p>
                                <div class="invalid-feedback-coach d-none text-danger" id="editCoachPassword-Error"></div>
                            </div>

                            <div class="col-md-12 d-flex align-items-center justify-content-end" style="min-height: 40px">
                                <button class="cta" id="update-coach-button">Update Coach</button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>


        <!--Guardian Modal -->


        <div class="modal fade" id="guardian-add-modal" tabindex="-1" aria-labelledby="guardianAddLabel"
            aria-hidden="true" data-bs-backdrop="static">
            <div class="modal-dialog modal-dialog-centered" style="max-width: 1160px">
                <div class="modal-content">
                    <div class="modal-body p-5">
                        <span class="modal-close" data-bs-dismiss="modal" id="cancel-guardian-add">&#x2715;</span>
                        <form class="form row" id="submit-Guardian-Data">
                            <div id="guardianSuccessMessage" class="alert alert-success d-none" role="alert"></div>
                            <div id="guardianErrorMessage" class="alert alert-danger d-none" role="alert"></div>
                            @csrf
                            <div class="mb-4 col-md-6">
                                <label class="form-label text-uppercase" for="guardianFirstName">First Name</label>
                                <input class="form-control" id="guardianFirstName" type="text" name="firstName" />
                                <div class="invalid-feedback-guardian d-none text-danger" id="guardianFirstName-Error">
                                </div>
                            </div>
                            <div class="mb-4 col-md-6">
                                <label class="form-label text-uppercase" for="guardianLastName">Last Name</label>
                                <input class="form-control" id="guardianLastName" type="text" name="lastName" />
                                <div class="invalid-feedback-guardian d-none text-danger" id="guardianLastName-Error">
                                </div>
                            </div>
                            <div class="mb-4 col-md-6">
                                <label class="form-label text-uppercase" for="guardianEmail">Email</label>
                                <input class="form-control" id="guardianEmail" type="email" name="email" />
                                <div class="invalid-feedback-guardian d-none text-danger" id="guardianEmail-Error"></div>
                            </div>
                            <div class="mb-4 col-md-6">
                                <label class="form-label text-uppercase" for="guardianPassword">Password</label>
                                <input class="form-control" id="guardianPassword" type="password" name="password" />
                                <div class="invalid-feedback-guardian d-none text-danger" id="guardianPassword-Error">
                                </div>
                            </div>
                            <div class="mb-4 col-md-12">
                                <label class="form-label text-uppercase" for="guardianMobile">Phone</label>
                                <input class="form-control" id="guardianMobile" type="text" name="mobile_number" />
                                <div class="invalid-feedback-guardian d-none text-danger" id="guardianMobile-Error"></div>
                            </div>

                            <!-- Checkbox for Make Coach -->
                            <div class="mb-4 col-md-12">
                                <input type="checkbox" id="makeCoachCheckbox" class="styled-checkbox"
                                    name="makeCoach" />
                                <label for="makeCoachCheckbox" class="form-label text-uppercase ms-2">Make Coach</label>
                            </div>

                            <!-- Town and Program fields (initially hidden) -->
                            <div id="coachDetails" class="d-none">
                                <div class="mb-4 col-md-12">
                                    <label class="form-label text-uppercase" for="guardianTown">Town</label>
                                    <input class="form-control" id="guardianTown" type="text" name="town" />
                                    <div class="invalid-feedback-guardian d-none text-danger" id="guardianTown-Error">
                                    </div>
                                </div>
                                <div class="mb-4 col-md-12">
                                    <label class="form-label text-uppercase" for="guardianTeam">Team Name</label>
                                    <input class="form-control" id="guardianTeam" type="text" name="team" />
                                    <div class="invalid-feedback-guardian d-none text-danger" id="guardianTeam-Error">
                                    </div>
                                </div>
                            </div>

                            <div class="col-md-12 d-flex align-items-center justify-content-end" style="min-height: 40px">
                                <button class="cta" id="add-guardian-button">Add Guardian</button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>




        <!--Edit Guardian Modal -->

        <div class="modal fade" id="guardian-edit-modal" tabindex="-1" aria-labelledby="guardianEditLabel"
            aria-hidden="true" data-bs-backdrop="static">
            <div class="modal-dialog modal-dialog-centered" style="max-width: 1160px">
                <div class="modal-content">
                    <div class="modal-body p-5">
                        <span class="modal-close" data-bs-dismiss="modal" id="cancel-guardian-edit">&#x2715;</span>
                        <form class="form row" id="update-Guardian-Data">
                            <div id="guardianEditSuccessMessage" class="alert alert-success d-none" role="alert"></div>
                            <div id="guardianEditErrorMessage" class="alert alert-danger d-none" role="alert"></div>
                            @csrf
                            <input type="hidden" id="editGuardianId" name="id" />

                            <div class="mb-4 col-md-6">
                                <label class="form-label text-uppercase" for="editGuardianFirstName">First Name</label>
                                <input class="form-control" id="editGuardianFirstName" type="text"
                                    name="firstName" />
                                <div class="invalid-feedback-guardian d-none text-danger"
                                    id="editGuardianFirstName-Error">
                                </div>
                            </div>

                            <div class="mb-4 col-md-6">
                                <label class="form-label text-uppercase" for="editGuardianLastName">Last Name</label>
                                <input class="form-control" id="editGuardianLastName" type="text" name="lastName" />
                                <div class="invalid-feedback-guardian d-none text-danger" id="editGuardianLastName-Error">
                                </div>
                            </div>

                            <div class="mb-4 col-md-6">
                                <label class="form-label text-uppercase" for="editGuardianEmail">Email</label>
                                <input class="form-control" id="editGuardianEmail" type="email" name="email" />
                                <div class="invalid-feedback-guardian d-none text-danger" id="editGuardianEmail-Error">
                                </div>
                            </div>

                            <div class="mb-4 col-md-6">
                                <label class="form-label text-uppercase" for="editGuardianPassword">Password</label>
                                <input class="form-control" id="editGuardianPassword" type="password" name="password" />
                                <div class="invalid-feedback-guardian d-none text-danger" id="editGuardianPassword-Error">
                                </div>
                                <p class="text-muted mt-2">Leave Blank to keep old Password</p>
                            </div>

                            <div class="mb-4 col-md-6">
                                <label class="form-label text-uppercase" for="editGuardianMobile">Phone</label>
                                <input class="form-control" id="editGuardianMobile" type="text"
                                    name="mobile_number" />
                                <div class="invalid-feedback-guardian d-none text-danger"
                                    id="editGuardianMobile_number-Error">
                                </div>
                            </div>

                            <div class="col-md-12 d-flex align-items-center justify-content-end" style="min-height: 40px">
                                <button class="cta" id="update-guardian-button">Update Guardian</button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
        <script>
            const formActionBase = "{{ url('admin/delete') }}";
            const csrfToken = "{{ csrf_token() }}";
        </script>

        <script
            src="{{ asset('js/admin.allUsers.js
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                        ') }}">
        </script>


    </section>
@endsection
