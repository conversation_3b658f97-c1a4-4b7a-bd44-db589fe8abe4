<?php

namespace App\Http\Controllers;

use App\Events\PlayerInvitedByAdmin;
use App\Models\AdminInvitesPlayerForProgram;
use App\Models\EarlyBirdPricing;
use App\Models\GuardianPayment;
use App\Models\PlayerInvitation;
use App\Models\ProgramRegistration;
use App\Models\PlayerProgram;
use App\Models\Program;
use App\Models\Role;
use App\Models\Team;
use App\Models\TeamCoach;
use App\Models\TeamPlayer;
use App\Models\TeamProgram;
use App\Models\User;
use Exception;
use Illuminate\Contracts\View\View;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Hash;
use Carbon\Carbon;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Validator;
use Illuminate\Validation\Rule;
use Illuminate\Support\Facades\Log;




class AdminController extends Controller
{
    private function determineSeason($startDate)  // created function for storing season in database by getting the starting month
    {
        $start = Carbon::parse($startDate);

        $month = $start->month;

        if ($month >= 9 && $month <= 11) {
            return 'Fall';
        } elseif ($month == 12 || $month <= 2) {
            return 'Winter';
        } elseif ($month >= 3 && $month <= 5) {
            return 'Spring';
        } else {
            return 'Summer';
        }
    }


    public function index(Request $request)
    {
        return view('admin.dashboard');
    }


    // chaniging all users into livewire

    public function allUsers(Request $request)
    {

        return view('admin.allUsers');
    }








    // public function allUsers(Request $request)
    // {
    //     try {
    //         $query = User::query();
    //         $query->where('email', '!=', '<EMAIL>');

    //         if ($request->filled('search')) {
    //             $search = $request->input('search');

    //             if ($request->filled('filter')) {
    //                 $filter = $request->input('filter');

    //                 switch ($filter) {
    //                     case 'Town':
    //                         $query->where('town', 'like', "%{$search}%");
    //                         break;
    //                     case 'Age':
    //                         $query->where('age', 'like', "%{$search}%");
    //                         break;
    //                     case 'Grade':
    //                         $query->where('grade', 'like', "%{$search}%");
    //                         break;
    //                     default:
    //                         $query->where(function ($q) use ($search) {
    //                             $q->where('firstName', 'like', "%{$search}%")
    //                                 ->orWhere('lastName', 'like', "%{$search}%")
    //                                 ->orWhere('email', 'like', "%{$search}%");
    //                         });
    //                         break;
    //                 }
    //             } else {
    //                 $query->where(function ($q) use ($search) {
    //                     $q->where('firstName', 'like', "%{$search}%")
    //                         ->orWhere('lastName', 'like', "%{$search}%")
    //                         ->orWhere('email', 'like', "%{$search}%")
    //                         ->orWhere('town', 'like', "%{$search}%")
    //                         ->orWhere('grade', 'like', "%{$search}%");
    //                 });
    //             }
    //         }

    //         if ($request->filled('filter')) {
    //             $filter = $request->input('filter');
    //             switch ($filter) {
    //                 case 'Town':
    //                     $query->orderBy('town');
    //                     break;
    //                 case 'Age':
    //                     $query->orderBy('age');
    //                     break;
    //                 case 'Grade':
    //                     $query->orderBy('grade');
    //                     break;
    //                 default:
    //                     $query->orderBy('firstName');
    //                     break;
    //             }
    //         }

    //         $users = $query->paginate(10);

    //         if ($request->expectsJson()) {
    //             $pagination = $users->onEachSide(1)->links('pagination::bootstrap-5')->render();
    //             return response()->json([
    //                 'users' => $users->map(function ($user) {
    //                     return [
    //                         'id' => $user->id,
    //                         'firstName' => $user->firstName,
    //                         'lastName' => $user->lastName,
    //                         'email' => $user->email,
    //                         'town' => $user->town,
    //                         'age' => $user->age,
    //                         'grade' => $user->grade,
    //                         'roles' => $user->roles->map(function ($role) {
    //                             return ['name' => $role->name];
    //                         })->toArray(),
    //                     ];
    //                 }),
    //                 'pagination' => $pagination,
    //             ]);
    //         }

    //         return view('admin.allUsers', compact('users'));
    //     } catch (Exception $e) {
    //         return response()->json(['error' => 'An error occurred'], 500);
    //     }
    // }

    // api's

    public function getGuardians()
    {
        $guardians = User::withRole('guardian')
            ->where('is_joined', true)
            ->get();

    return response()->json($guardians);
}

/**
 * Get all coaches for the team creation dropdown
 */
public function getCoaches()
{
    $coaches = User::withRole('coach')
        ->where('is_joined', true)
        ->get(['id', 'firstName', 'lastName']);

    return response()->json($coaches);
}

/**
 * Get all programs with type 'Team' for the team creation dropdown
 */
public function getTeamPrograms()
{
    $programs = Program::where('type', 'Team')
        ->get(['id', 'name']);

    return response()->json($programs);
}

/**
 * Create a new team and associate it with a coach and program
 */
public function createTeam(Request $request)
{
    $request->validate([
        'teamName' => 'required|string|max:255',
        'coach_id' => 'required|exists:users,id',
        'program_id' => 'required|exists:programs,id',
    ]);

    try {
        DB::beginTransaction();

        // Create the team
        $team = new Team();
        $team->name = $request->teamName;
        $team->user_id = $request->coach_id;
        $team->save();

        // Associate the coach with the team
        TeamCoach::create([
            'team_id' => $team->id,
            'coach_id' => $request->coach_id,
            'is_primary' => true
        ]);

        // Associate the team with the program
        TeamProgram::create([
            'team_id' => $team->id,
            'program_id' => $request->program_id
        ]);

        DB::commit();

        return response()->json([
            'success' => true,
            'message' => 'Team created successfully!'
        ]);
    } catch (\Exception $e) {
        DB::rollBack();
        return response()->json([
            'success' => false,
            'message' => 'Failed to create team.',
            'error' => $e->getMessage()
        ], 500);
    }
}



    public function guardianOfPlayer()
    {
        $guardians = User::withRole('guardian')
            ->where('is_joined', true)
            ->get();

        return response()->json($guardians);
    }


    public function store_player(Request $request)
    {

        $validator = Validator::make($request->all(), [
            'firstName' => 'required|string|max:255',
            'lastName' => 'required|string|max:255',
            'email' => 'nullable|email|unique:users,email',
            'gender' => 'required',
            'grade' => 'required',
            'birthDate' => 'required|date',
            'street' => 'required|string|max:500',
            'town' => 'required|string',
            'state' => 'required|string',
            'guardian-name' => 'required|exists:users,id',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'errors' => $validator->errors()
            ], 422);
        }


        try {
            if ($request->hasFile('profilePhoto')) {
                $profilePhoto = $request->file('profilePhoto');
                $fileName = time() . '.' . $profilePhoto->getClientOriginalExtension();
                $profilePhoto->storeAs('public', $fileName);
                $profilePhotoPath = $fileName;
            } else {
                $profilePhotoPath = null;
            }
            $birthDate = Carbon::parse($request->input('birthDate'));
            $age = $birthDate->age;
            $email = $request->input('email') ?: null;




            $guardianDetail = User::find($request->input('guardian-name'));

            if ($guardianDetail->parent_id == null) {

                $parent_id = $guardianDetail->id;

                $primary_parent_id = $guardianDetail->id;
            } else {
                $parent_id = $guardianDetail->id;
                $primary_parent_id = $guardianDetail->primary_parent_id;
            }

            $user = User::create([
                'firstName' => $request->input('firstName'),
                'lastName' => $request->input('lastName'),
                'email' => $email,
                'birthDate' => $request->input('birthDate'),
                'age' => $age,
                'grade' => $request->input('grade'),
                'gender' => $request->input('gender'),
                'street' => $request->input('street'),
                'town' => $request->input('town'),
                'state' => $request->input('state'),
                'profilePhoto' => $profilePhotoPath,
                'parent_id' => $parent_id,
                'primary_parent_id' => $primary_parent_id,
            ]);

            // if ($request->has('guardian-name')) {
            // $guardianId = $request->input('guardian-name');

            // DB::table('user_guardians')->insert([
            // 'user_id' => $user->id,
            // 'guardian_id' => $guardianId
            // ]);
            // }

            $user->roles()->attach(Role::PLAYER);


            return response()->json([
                'success' => true,
                'message' => 'Player added successfully.',
            ]);
        } catch (Exception $e) {
            return response()->json(['error' => false, 'message' => 'An error occurred while creating the player account.
          Please try again.']);
        }
    }



    public function show_player($id)
    {
        try {
            $player = User::where('id', $id)
                ->withRole('player')
                ->first();

            if (!$player) {
                return response()->json(['error' => 'Player not found'], 404);
            }

            $profilePhotoUrl = $player->profilePhoto
                ? url(Storage::url($player->profilePhoto))
                : '';


            return response()->json([
                'player' => $player,
                'profilePhotoUrl' => $profilePhotoUrl,
            ]);
        } catch (Exception $e) {
            return response()->json(['error' => 'An error occurred: ' . $e->getMessage()], 500);
        }
    }


    public function edit_player(Request $request, $playerId)
    {

        $request->validate([
            'firstName' => 'required|string|max:255',
            'lastName' => 'required|string|max:255',
            'email' => 'nullable|email|',
            'gender' => 'required',
            'birthDate' => 'required|date',
            'grade' => 'required|string',
            'street' => 'required|string',
            'town' => 'required|string',
            'state' => 'required|string',
            'guardianId' => 'required',
            'profilePhoto' => 'nullable|image|max:2048',
        ], [
            'guardianId.required' => 'You must select a guardian for a player.',

        ]);

        try {

            $player = User::findOrFail($playerId);

            $birthDate = Carbon::parse($request->input('birthDate'));
            $age = $birthDate->age;
            $email = $request->input('email') ?: null;


            $guardianDetail = User::find($request->input('guardianId'));

            if ($guardianDetail->parent_id == null) {

                $parent_id = $guardianDetail->id;

                $primary_parent_id = $guardianDetail->id;
            } else {
                $parent_id = $guardianDetail->id;
                $primary_parent_id = $guardianDetail->parent_id;
            }
            $player->update([
                'firstName' => $request->firstName,
                'lastName' => $request->lastName,
                'grade' => $request->grade,
                'birthDate' => $request->birthDate,
                'age' => $age,
                'gender' => $request->gender,
                'parent_id' => $parent_id,
                'primary_parent_id' => $primary_parent_id,
                'email' => $email,
                'street' => $request->street,
                'town' => $request->town,
                'state' => $request->state,
            ]);


            if ($request->hasFile('profilePhoto')) {
                $profilePhoto = $request->file('profilePhoto');
                $fileName = time() . '.' . $profilePhoto->getClientOriginalExtension();
                $profilePhoto->storeAs('public', $fileName);
                $player->profilePhoto = $fileName;
                $player->save();
            }


            return response()->json([
                'success' => true,
                'message' => 'Player updated successfully!'
            ]);
        } catch (Exception $e) {
            return response()->json(['error' => false, 'message' => 'An error occurred while creating the Player account.
                        Please try again.']);
        }
    }

    public function store_admin(Request $request)
    {

        $validator = Validator::make($request->all(), [
            'firstName' => 'required|string|max:255',
            'lastName' => 'required|string|max:255',
            'email' => 'required|unique:users,email',
            'password' => 'required|min:8',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'errors' => $validator->errors()
            ], 422);
        }


        try {


            $user = User::create([
                'firstName' => $request->input('firstName'),
                'lastName' => $request->input('lastName'),
                'email' => $request->input('email'),
                'password' => Hash::make($request->input('password')),
            ]);

            $user->roles()->attach(Role::ADMIN);

            return response()->json([
                'success' => true,
                'message' => 'Admin added successfully.',
            ]);
        } catch (Exception $e) {
            return response()->json(['error' => false, 'message' => 'An error occurred while creating the Admin account.
      Please try again.']);
        }
    }

    public function show_admin($id)
    {

        try {

            $admin = User::where('id', $id)
                ->first();

            if (!$admin) {
                return response()->json(['error' => 'Admin not found'], 404);
            }

            return response()->json($admin);
        } catch (Exception $e) {
            return response()->json(['error' => 'An error occurred: ' . $e->getMessage()], 500);
        }
    }

    public function edit_admin(Request $request, $id)
    {

        $request->validate([
            'firstName' => 'required|string|max:255',
            'lastName' => 'required|string|max:255',
            'email' => 'required|email|max:255',

        ]);

        try {

            $admin = User::findOrFail($id);


            $data = [
                'firstName' => $request->firstName,
                'lastName' => $request->lastName,
                'email' => $request->email,
            ];


            if ($request->filled('password')) {
                $data['password'] = Hash::make($request->password);
            }


            $admin->update($data);

            return response()->json([
                'success' => true,
                'message' => 'Admin updated successfully!'
            ]);
        } catch (Exception $e) {
            return response()->json(['error' => false, 'message' => 'An error occurred while updating the Admin account.
     Please try again.']);
        }
    }


    public function store_coach(Request $request)
    {

        $validator = Validator::make($request->all(), [
            'firstName' => 'required|string|max:255',
            'lastName' => 'required|string|max:255',
            'townOrProgram' => 'required',
            'teamName' => 'required|string',
            'email' => 'required|unique:users,email',
            'password' => 'required|min:8',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'errors' => $validator->errors()
            ], 422);
        }
        try {
            $user = User::create([
                'firstName' => $request->input('firstName'),
                'lastName' => $request->input('lastName'),
                'town' => $request->input('townOrProgram'),
                'teamName' => $request->input('teamName'),
                'email' => $request->input('email'),
                'password' => Hash::make($request->input('password')),
                'is_joined' => true
            ]);

            $user->roles()->attach(Role::COACH);

            $team = Team::create([

                'user_id' => $user->id,
                'name' => $request->input('teamName'),

            ]);

            TeamCoach::create([
                'team_id' => $team->id,
                'coach_id' => $user->id,
                'is_primary' => true,
            ]);

            return response()->json([
                'success' => true,
                'message' => 'Coach added successfully.',
            ]);
        } catch (Exception $e) {
            return response()->json(['error' => false, 'message' => 'An error occurred while creating the Coach account.
     Please try again.']);
        }
    }

    public function show_coach($id)
    {

        try {

            $coach = User::where('id', $id)
                ->first();

            if (!$coach) {
                return response()->json(['error' => 'Coach not found'], 404);
            }

            return response()->json($coach);
        } catch (Exception $e) {
            return response()->json(['error' => 'An error occurred: ' . $e->getMessage()], 500);
        }
    }


    public function edit_coach(Request $request, $id)
    {
        $coach = User::findOrFail($id);

        $request->validate([
            'firstName' => 'required|string|max:255',
            'lastName' => 'required|string|max:255',
            'email' => [
                'required',
                'email',
                'max:255',
                Rule::unique('users')->ignore($coach->id),
            ],
            'teamName' => 'required',
            'townOrProgram' => 'required',
            'password' => 'nullable|min:8'
        ]);

        try {
            $data = [
                'firstName' => $request->firstName,
                'lastName' => $request->lastName,
                'email' => $request->email,
                'teamName' => $request->teamName,
                'town' => $request->townOrProgram,
            ];

            if ($request->filled('password')) {
                $data['password'] = Hash::make($request->password);
            }

            $coach->update($data);

            $coachTeam = Team::where('user_id', $id)->first();
            if ($coachTeam) {
                $coachTeam->update([
                    'name' => $request->teamName,
                ]);
            }

            return response()->json([
                'success' => true,
                'message' => 'Coach updated successfully!'
            ]);
        } catch (Exception $e) {
            return response()->json(['error' => true, 'message' => 'An error occurred while updating the Coach account. Please try again.']);
        }
    }


    public function store_guardian(Request $request)
    {



        $validator = Validator::make($request->all(), [
            'firstName' => 'required|string|max:255',
            'lastName' => 'required|string|max:255',
            'email' => 'required|unique:users,email',
            'password' => 'required|min:8',
            'mobile_number' => [
                'required',
                'regex:/^\(?\d{3}\)?[\s.-]?\d{3}[\s.-]?\d{4}$/'
            ],
        ]);

        $isCoach = $request->has('makeCoach');

        if ($isCoach) {

            $request->validate([
                'town' => 'required|string|max:255',
                'team' => 'required|string|max:255'
            ]);
        }

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'errors' => $validator->errors()
            ], 422);
        }


        try {

            $user = User::create([
                'firstName' => $request->input('firstName'),
                'lastName' => $request->input('lastName'),
                'email' => $request->input('email'),
                'mobile_number' => $request->input('mobile_number'),
                'password' => Hash::make($request->input('password')),
                'is_joined' => true,
            ]);

            $user->roles()->attach(Role::GUARDIAN);

            if ($isCoach) {

                $user->town = $request->town;
                $user->teamName = $request->team;
                $user->is_coach = true;

                $user->roles()->attach(Role::COACH);
                $user->save();

                $team = Team::create([

                    'user_id' => $user->id,
                    'name' => $request->team,

                ]);

                TeamCoach::create([
                    'coach_id' => $user->id,
                    'team_id' => $team->id,
                    'is_primary' => true,
                ]);
            }

            return response()->json([
                'success' => true,
                'message' => 'Guardian added successfully.',
            ]);
        } catch (Exception $e) {
            return response()->json(['error' => false, 'message' => 'An error occurred while creating the Guardian account.
         Please try again.']);
        }
    }


    public function show_guardian($id)
    {

        try {

            $guardian = User::where('id', $id)
                ->first();

            if (!$guardian) {
                return response()->json(['error' => 'Guardian not found'], 404);
            }

            return response()->json($guardian);
        } catch (Exception $e) {
            return response()->json(['error' => 'An error occurred: ' . $e->getMessage()], 500);
        }
    }


    public function edit_guardian(Request $request, $id)
    {
        $guardian = User::findOrFail($id);

        $request->validate([
            'firstName' => 'required|string|max:255',
            'lastName' => 'required|string|max:255',
            'email' => [
                'required',
                'email',
                'max:255',
                Rule::unique('users')->ignore($guardian->id),
            ],
            'password' => 'nullable|min:8',
            'mobile_number' => [
                'required',
                'regex:/^\(?\d{3}\)?[\s.-]?\d{3}[\s.-]?\d{4}$/'
            ],
        ]);

        try {
            $data = [
                'firstName' => $request->firstName,
                'lastName' => $request->lastName,
                'email' => $request->email,
                'mobile_number' => $request->mobile_number,
            ];

            if ($request->filled('password')) {
                $data['password'] = Hash::make($request->password);
            }

            $guardian->update($data);

            return response()->json([
                'success' => true,
                'message' => 'Guardian updated successfully!'
            ]);
        } catch (Exception $e) {
            return response()->json(['error' => true, 'message' => 'An error occurred while updating the Guardian account. Please try again.']);
        }
    }







    public function destroy_user(Request $request, User $user)
    {

        $user->delete();
        return redirect()->route('admin.users')->with('success', 'User deleted successfully.');
    }





    //programs routes---->
    public function allPrograms(Request $request)
    {
        $gender = $request->input('gender-filter');
        $ageGroup = $request->input('age-group-filter');
        $sport = $request->input('sport-filter');

        $query = Program::query();

        if ($sport) {
            $query->sport($sport); // used query scopes here, check the program model for this.
        }


        if ($gender) {
            $query->where('gender', $gender);
        }

        if ($ageGroup) {
            $query->ageGroup($ageGroup); // used query scope here too.
        }

        $query->orderBy('created_at', 'desc');

        $programs = $query->paginate(30);

        if ($request->ajax()) {
            return response()->json([
                'programData' => view('admin.partials.programs', compact('programs'))->render(),
                'pagination' => $programs->appends($request->query())->links('pagination::bootstrap-5')->render(),
            ]);
        }


        return view('admin.allPrograms', compact('programs'));
    }




    public function program_create()
    {
        return view('admin.addProgram');
    }



    public function program_store(Request $request)
    {

    // Base validation rules
    $data = $request->validate([
        'name' =>                      'required|string',
        'type' =>                      'required|string',
        'sub_program' =>               'required|string',
        'location' =>                  'required',
        'sport' =>                     'required|string',
        'gender' =>                    'required|string',
        'age_restriction_from' =>      'nullable|integer',
        'age_restriction_to' =>        'nullable|integer|gte:age_restriction_from',
        'grade' =>                     'required|string',
        'birth_date_cutoff' =>         'required|date',
        'registration_opening_date' => 'required|date',
        'registration_closing_date' => 'required|date|after_or_equal:registration_opening_date',
        'start_date' =>                'required|date|after_or_equal:registration_closing_date',
        'end_date' =>                  'required|date|after_or_equal:start_date',
        'start_time' =>                'required|date_format:H:i',
        'end_time' =>                  'required|date_format:H:i',
        'frequency' =>                 'required|string',
        'frequency_days' =>            'required|array',
        'number_of_registers' =>       'nullable|integer',
        'enable_waitlist' =>           'nullable|boolean',
        'cost' =>                      'required|numeric',
        'enable_early_bird_specials' =>'nullable|boolean',
        'payment' =>                   'required|in:full,recurring,split',
        'program_description' =>       'required|string',
        'price_on_or_after' =>         'nullable|array',
        'early_bird_specials_date' =>  'nullable|date',
        'status' =>                   'required|in:draft,public,private',
    ], [


        'age_restriction_to.gte' => 'The "age restriction end" must be greater than or equal to "age restriction start".',
        'registration_closing_date.after_or_equal' => 'The registration closing date must be after or equal to the registration opening date.',
        'start_date.after_or_equal' => 'The start date must be after or equal to the registration closing date.',
        'end_date.after_or_equal' => 'The end date must be after or equal to the start date.',
    ]);

        if ($request->input('payment') === 'recurring') {
            $request->validate([
                'minimumMonthlyPayment' => 'required|numeric|min:1|max:' . $request->input('cost'),
            ], [
                'minimumMonthlyPayment.required' => 'The minimum monthly payment is required for recurring payments.',
                'minimumMonthlyPayment.numeric' => 'The minimum monthly payment must be a valid number.',
                'minimumMonthlyPayment.min' => 'The minimum monthly payment must be at least 1.',
                'minimumMonthlyPayment.max' => 'The minimum monthly payment cannot exceed the program cost.',
            ]);

            $data['minimum_recurring_amount'] = $request->input('minimumMonthlyPayment');
        }


        $season = $this->determineSeason($data['start_date']);
        $data['season'] = $season;


        if (!$data['enable_early_bird_specials']) {
            $data['early_bird_specials_date'] = null;
        }

        if ($data['enable_early_bird_specials']) {
            $request->validate([
                'early_bird_specials_date' => 'nullable|date',
                'early_bird_from.0' => 'required|numeric',
                'early_bird_to.0' => 'required|numeric',
                'price_before.0' => 'required|numeric',
                'price_on_or_after.0' => 'required|numeric',
            ], [
                'early_bird_from.0.required' => 'Please enter a value for early bird from.',
                'early_bird_to.0.required' => 'Please enter a value for early bird to.',
                'price_before.0.required' => 'Please enter a value for price before.',
                'price_on_or_after.0.required' => 'Please enter a value for price on or after.',
            ]);
        }


        $program = Program::create($data);


        if ($data['enable_early_bird_specials'] && $request->has('early_bird_from')) {
            foreach ($request->input('early_bird_from') as $key => $value) {
                if (
                    !is_null($value) && $value !== '' &&
                    !is_null($request->input('early_bird_to')[$key]) && $request->input('early_bird_to')[$key] !== '' &&
                    !is_null($request->input('price_before')[$key]) && $request->input('price_before')[$key] !== '' &&
                    !is_null($request->input('price_on_or_after')[$key]) && $request->input('price_on_or_after')[$key] !== ''
                ) {
                    EarlyBirdPricing::create([
                        'program_id' => $program->id,
                        'from' => $value,
                        'to' => $request->input('early_bird_to')[$key],
                        'price_before' => $request->input('price_before')[$key],
                        'price_on_or_after' => $request->input('price_on_or_after')[$key],
                    ]);
                }
            }
        }

        return redirect()->back()->with('success', 'Program added successfully!');
    }




    public function program_edit($slug)
    {

        $program = Program::where('slug', $slug)->firstOrFail();


        $earlyBirdPricing = EarlyBirdPricing::where('program_id', $program->id)->get();


        $frequencyDays = is_array($program->frequency_days) ? $program->frequency_days : json_decode($program->frequency_days, true);


        return view('admin.editProgram', compact('program', 'earlyBirdPricing', 'frequencyDays'));
    }



    public function program_update(Request $request, $slug)
    {


       $data = $request->validate([
        'name' => 'required|string',
        'type' => 'required|string',
        'sub_program' => 'required|string',
        'location' => 'required',
        'sport' => 'required|string',
        'gender' => 'required|string',
        'age_restriction_from' => 'nullable|integer',
        'age_restriction_to' => 'nullable|integer|gte:age_restriction_from',
        'grade' => 'required|string',
        'birth_date_cutoff' => 'required|date',
        'status' => 'required|in:draft,public,private',
        'registration_opening_date' => 'required|date',
        'registration_closing_date' => 'required|date|after_or_equal:registration_opening_date',
        'start_date' => 'required|date|after_or_equal:registration_closing_date',
        'end_date' => 'required|date|after_or_equal:start_date',
        'start_time' => 'required',
        'end_time' => 'required',
        'frequency' => 'required|string',
        'frequency_days' => 'required|array',
        'number_of_registers' => 'nullable|integer',
        'enable_waitlist' => 'nullable|boolean',
        'cost' => 'required|numeric',
        'enable_early_bird_specials' => 'nullable|boolean',
        'payment' => 'required|in:full,recurring,split',
        'program_description' => 'required|string',
        'price_on_or_after' => 'nullable|array',
        'early_bird_specials_date' => 'nullable|date',
    ], [
        'age_restriction_from.required' => 'The "age restriction start" is required.',
        'age_restriction_to.required' => 'The "age restriction end" is required.',
        'age_restriction_to.gte' => ' The "age restriction start" must be greater than or equal to "age restriction end".',
        'registration_closing_date.after_or_equal' => 'The registration closing date must be after or equal to the registration opening date.',
        'start_date.after_or_equal' => 'The start date must be after or equal to the registration closing date.',
        'end_date.after_or_equal' => 'The end date must be after or equal to the start date.',
    ]);
         if($data['enable_early_bird_specials']){
           $request->validate([
        'early_bird_specials_date' => 'nullable|date',
         'early_bird_from.0' => 'required|numeric',
         'early_bird_to.0' => 'required|numeric',
         'price_before.0' => 'required|numeric',
         'price_on_or_after.0' => 'required|numeric',
], [
    'early_bird_from.0.required' => 'Please enter a value for early bird from.',
        'early_bird_to.0.required' => 'Please enter a value for early bird to.',
        'price_before.0.required' => 'Please enter a value for price before.',
        'price_on_or_after.0.required' => 'Please enter a value for price on or after.',


            ]);
        }


        if ($request->input('payment') === 'recurring') {
            $request->validate([
                'minimumMonthlyPayment' => 'required|numeric|min:1|max:' . $request->input('cost'),
            ], [
                'minimumMonthlyPayment.required' => 'The minimum monthly payment is required for recurring payments.',
                'minimumMonthlyPayment.numeric' => 'The minimum monthly payment must be a valid number.',
                'minimumMonthlyPayment.min' => 'The minimum monthly payment must be at least 1.',
                'minimumMonthlyPayment.max' => 'The minimum monthly payment cannot exceed the program cost.',
            ]);

            $data['minimum_recurring_amount'] = $request->input('minimumMonthlyPayment');
        } else {
            $data['minimum_recurring_amount'] = null;
        }
        $program = Program::where('slug', $slug)->firstOrFail();

        $season = $this->determineSeason($data['start_date']);

        $data['season'] = $season;

        $program->update($data);
        if ($data['enable_early_bird_specials']) {

            EarlyBirdPricing::where('program_id', $program->id)->delete();


            if ($request->has('early_bird_from')) {
                foreach ($request->input('early_bird_from') as $key => $value) {

                    if (
                        !is_null($value) && $value !== '' &&
                        !is_null($request->input('early_bird_to')[$key]) && $request->input('early_bird_to')[$key] !== '' &&
                        !is_null($request->input('price_before')[$key]) && $request->input('price_before')[$key] !== '' &&
                        !is_null($request->input('price_on_or_after')[$key]) && $request->input('price_on_or_after')[$key] !== ''
                    ) {
                        EarlyBirdPricing::create([
                            'program_id' => $program->id,
                            'from' => $value,
                            'to' => $request->input('early_bird_to')[$key],
                            'price_before' => $request->input('price_before')[$key],
                            'price_on_or_after' => $request->input('price_on_or_after')[$key],
                        ]);
                    }
                }
            }
        } else {
            EarlyBirdPricing::where('program_id', $program->id)->delete();
        }

        return redirect()->back()->with('success', 'Program updated successfully!');
    }


    public function program_destroy(Request $request, $program)
    {
        $program = Program::where('slug', $program)->first();

        if ($program) {
            $program->delete();
            return back()->with('success', 'Program deleted successfully');
        }
        return back()->with('error', 'Program not found');
    }



    public function paymentsAndRegistrations(Request $request)
    {

        $currentDate = Carbon::today()->toDateString();
        $query = Program::where('end_date', '>=', $currentDate);

        if ($request->has('sport-filter') && $request->input('sport-filter') != '') {
            $query->where('sport', $request->input('sport-filter'));
        }
        if ($request->has('search-input') && $request->input('search-input') != '') {
            $query->where('name', 'like', '%' . $request->input('search-input') . '%');
        }

        $programs = $query->paginate(30);





        // $programs = Program::where('end_date', '>=', $currentDate)->get();
        $programData = [];

        foreach ($programs as $program) {
            if ($program->type == 'Team') {
                $teamRegistrations = ProgramRegistration::where('program_id', $program->id)->get();
                $teamsData = [];

                $payments = DB::table('all_payments')
                    ->whereJsonContains('program_ids', (string)$program->id)  // Cast to string since your JSON contains string IDs
                    ->get();



                $totalPayments = $payments->sum('paid_amount');

                foreach ($teamRegistrations as $registration) {
                    $team = Team::where('id', $registration->team_id)->first();
                    if ($team) {
                        // $coach = TeamCoach::where('team_id', $team->id)
                        //     ->where('is_primary', false)
                        //     ->first();
                        // if (!$coach) {
                        //     $coach = TeamCoach::where('team_id', $team->id)
                        //         ->where('is_primary', true)
                        //         ->first();
                        // }



                        $primaryCoach = TeamCoach::where('team_id', $team->id)
                            ->where('is_primary', true)
                            ->first();

                        $assistantCoach = TeamCoach::where('team_id', $team->id)
                            ->where('is_primary', false)
                            ->first();


                        if ($primaryCoach) {
                            $coachData = User::where('id', $primaryCoach->coach_id)->first();
                            $assistantCoachEmail = $assistantCoach ? User::where("id", $assistantCoach->coach_id)->select('email')->first()->email : null;
                            $playerCount = PlayerInvitation::where('team_id', $team->id)
                                ->where('program_id', $program->id)
                                ->count();

                            $teamBalance = PlayerInvitation::where('team_id', $team->id)
                                ->where('program_id', $program->id)
                                ->get(['balance_assigned', 'balance_due'])
                                ->reduce(function ($carry, $invitation) {
                                    return $carry + ($invitation->balance_assigned - $invitation->balance_due);
                                }, 0);

                            $teamsData[] = [
                                'team_id' => $team->id,
                                'team_name' => $team->name,
                                'coach_slug' => $coachData->slug,
                                'coach_firstName' => $coachData->firstName,
                                'coach_lastName' => $coachData->lastName,
                                'coach_email' => $coachData->email,
                                'assistantCoachEmail' => $assistantCoachEmail,
                                'player_count' => $playerCount,
                                'team_balance' => $teamBalance,
                            ];
                        }
                    }
                }

                $programData[] = [
                    'id' => $program->id,
                    'program_slug' => $program->slug,
                    'name' => $program->name,
                    'registrations' => $teamRegistrations->count(),
                    'payments' => $totalPayments,
                    'type' => $program->type,
                    'teams' => $teamsData,
                ];
            } elseif ($program->type == "Individual" || $program->type == "AAU") {
                $individualRegistrations = ProgramRegistration::where('program_id', $program->id)->get();
                $playersData = [];
                $payments = DB::table('all_payments')
                    ->whereJsonContains('program_ids', (string)$program->id)  // Cast to string since your JSON contains string IDs
                    ->get();
                // $payments = GuardianPayment::where('program_id', $program->id)->get();
                $totalPayments = $payments->sum('paid_amount');

                foreach ($individualRegistrations as $registration) {
                    $player = User::where('id', $registration->player_id)->first();
                    if ($player) {
                        $guardian = User::where('id', $player->primary_parent_id)->first();
                        $playersData[] = [
                            'player_id' => $player->id,
                            'player_name' => $player->firstName . ' ' . $player->lastName,
                            'player_email' => $player->email,
                            'guardian_email' => $guardian?->email,
                            'program_dates' => [
                                'start_date' => $program->start_date,
                                'end_date' => $program->end_date,
                            ],
                        ];
                    }
                }

                $programData[] = [
                    'id' => $program->id,
                    'program_slug' => $program->slug,
                    'name' => $program->name,
                    'registrations' => $individualRegistrations->count(),
                    'payments' => $totalPayments,
                    'type' => $program->type,
                    'players' => $playersData,
                ];
            }
        }

        if ($request->ajax()) {
            $pagination = $programs->onEachSide(1)->links('pagination::bootstrap-5')->render();
            return response()->json([
                'programData' => view('admin.payments.partialPaymentsView', compact('programData', 'programs'))->render(),
                'pagination' => $pagination,
            ]);
        }

        return view('admin.payments.payments', compact('programData', 'programs'));
    }





    public function allTeams(Request $request, $program)
    {
        $programDetail = Program::where('slug', $program)->first();

        if (!$programDetail) {
            return response()->json(['message' => 'Program not found'], 404);
        }

        $programId = $programDetail->id;
        $filteredTeamIdsQuery = DB::table('teams')
            ->join('team_coach', 'teams.id', '=', 'team_coach.team_id')
            ->join('users', 'team_coach.coach_id', '=', 'users.id')
            ->select('teams.id as team_id');


        $filters = [
            'coachFirstName' => 'users.firstName',
            'coachLastName' => 'users.lastName',
            'coachEmail' => 'users.email',
            'teamName' => 'teams.name',
        ];

        foreach ($filters as $inputKey => $column) {
            if ($request->filled($inputKey)) {
                $filteredTeamIdsQuery->orWhere($column, 'like', '%' . $request->input($inputKey) . '%');
            }
        }


        $filteredTeamIds = $filteredTeamIdsQuery->distinct()->pluck('team_id')->toArray();


        if (empty($filteredTeamIds)) {
            return response()->json([
                'teams' => [],
                'current_page' => 1,
                'last_page' => 1,
                'total' => 0,
                'message' => 'No teams found.',
                'success' => false,
            ]);
        }


        $query = DB::table('teams')
            ->join('team_coach', 'teams.id', '=', 'team_coach.team_id')
            ->join('users', 'team_coach.coach_id', '=', 'users.id')
            ->whereIn('teams.id', $filteredTeamIds)
            ->select(
                'teams.id as team_id',
                'teams.name as team_name',
                'users.id as coach_id',
                'users.firstName',
                'users.lastName',
                'users.email',
                'team_coach.is_primary'
            );


        $teams = $query->paginate(10);


        $registeredTeams = DB::table('team_program')
            ->where('program_id', $programId)
            ->pluck('team_id')
            ->toArray();


        $teamsGrouped = collect($teams->items())->groupBy('team_id');


        $teamsCollection = $teamsGrouped->map(function ($teamGroup) use ($registeredTeams) {
            $primaryCoach = $teamGroup->firstWhere('is_primary', true);
            $assistantCoaches = $teamGroup->filter(function ($coach) {
                return !$coach->is_primary;
            });


            $primaryCoachData = $primaryCoach ? [
                'id' => $primaryCoach->coach_id,
                'firstName' => $primaryCoach->firstName,
                'lastName' => $primaryCoach->lastName,
                'email' => $primaryCoach->email,
            ] : null;


            $assistantCoachEmails = $assistantCoaches->map(function ($coach) {
                return $coach->email;
            });

            return [
                'team_id' => $teamGroup[0]->team_id,
                'team_name' => $teamGroup[0]->team_name,
                'primary_coach' => $primaryCoachData,
                'assistant_coaches' => $assistantCoachEmails->toArray(),
                'is_registered' => in_array($teamGroup[0]->team_id, $registeredTeams),
            ];
        })->values();

        return response()->json([
            'teams' => $teamsCollection,
            'current_page' => $teams->currentPage(),
            'last_page' => $teams->lastPage(),
            'total' => $teams->total(),
            'message' => $teams->total() > 0 ? 'Teams found.' : 'No teams found.',
            'success' => true,
        ]);
    }







    public function editTeams(Request $request, $program, $team, $coach)
    {

        $programData = Program::where('slug', $program)->first();
        $teamData = Team::where('id', $team)->first();
        $coachData = User::where('slug', $coach)->first();


        if (!$programData || !$teamData || !$coachData) {
            abort(404, 'Data not found');
        }


        $teamDetail = PlayerInvitation::with('user')
            ->where('program_id', $programData->id)
            ->where('team_id', $teamData->id)
            ->get();


        return view('admin.payments.editTeamPlayer', [
            'teams' => $teamDetail,
            'teamName' => $teamData->name,
            'program' => $programData,
            'coach' => $coachData,
        ]);
    }


    public function addTeamToProgram(Request $request)
    {
        $request->validate([
            'team_id' => 'required',
            'coach_id' => 'required',
            'program_slug' => 'required',
        ]);




        $program = Program::where('slug', $request->program_slug)->first();
        $user = User::where('id', $request->coach_id)->first();


        if (!$program) {
            abort(404, 'Program not found');
        }

        if (!$user) {
            abort(404, 'Coach not found');
        }

        $existingTeamProgram = TeamProgram::where('team_id', $request->team_id)
            ->where('program_id', $program->id)
            ->first();

        if ($existingTeamProgram) {
            return response()->json(['success' => false, 'message' => 'The team is already registered for this program.'], 400);
        }

        $existingRegistration = ProgramRegistration::where('user_id', $request->coach_id)
            ->where('team_id', $request->team_id)
            ->where('program_id', $program->id)
            ->first();

        if ($existingRegistration) {
            return response()->json(['success' => false, 'message' => 'This coach is already registered for this team in the program.'], 400);
        }


        $programCost = $user->calculateAmountForProgram($program->id);


        TeamProgram::create([
            'team_id' => $request->team_id,
            'program_id' => $program->id,
        ]);


        ProgramRegistration::create([
            'user_id' => $request->coach_id,
            'team_id' => $request->team_id,
            'program_id' => $program->id,
            'amount' => $programCost,
            'is_paid' => true,
        ]);

        return response()->json(['success' => true, 'message' => 'Team registered successfully!'], 200);
    }



    public function removeTeamFromProgram(Request $request, $programSlug, $teamId, $coachSlug)
    {

        try {
            DB::transaction(function () use ($programSlug, $teamId, $coachSlug) {

                $programId = Program::where('slug', $programSlug)->value('id');
                $teamExists = Team::where('id', $teamId)->exists();
                $coachId = User::where('slug', $coachSlug)->value('id');

                if (!$programId) {
                    abort(404, 'Program not found.');
                }

                if (!$teamExists) {
                    abort(404, 'Team not found.');
                }

                if (!$coachId) {
                    abort(404, 'Coach not found.');
                }


                DB::table('team_program')
                    ->where('team_id', $teamId)
                    ->where('program_id', $programId)
                    ->delete();


                DB::table('program_registrations')
                    ->where('user_id', $coachId)
                    ->where('team_id', $teamId)
                    ->where('program_id', $programId)
                    ->delete();
            });

            return redirect()->back()->with('success', 'Team Removed From The Program Successfully.');
        } catch (Exception $e) {

            return redirect()->back()->with('error', 'An error occurred while removing the team. Please try again later.');
        }
    }

    public function deletePlayerFromTeam(Request $request)
    {
        try {

            DB::transaction(function () use ($request) {

                $playerInvitation = PlayerInvitation::where('user_id', $request->input('playerId'))
                    ->where('team_id', $request->input('teamId'))
                    ->where('coach_id', $request->input('coachId'))
                    ->where('program_id', $request->input('programId'))
                    ->first();


                if (!$playerInvitation) {
                    abort(404, 'Unable to find related player record.');
                }


                if ($playerInvitation->invitation_status === "accepted") {
                    TeamPlayer::where('team_id', $request->input('teamId'))
                        ->where('player_id', $request->input('playerId'))
                        ->delete();
                }


                $playerInvitation->delete();
            });

            return redirect()->back()->with('success', "Player removed from team successfully.");
        } catch (Exception $e) {
            return redirect()->back()->with('error', 'An error occurred while removing the player from the team.');
        }
    }

    //TODO if the request came from editteamPlayer page,then we will confirm how a player is added to the team and do some slight changes
    public function allPlayers(Request $request, $program)
    {

        $programDetail = Program::where('slug', $program)->first();

        if (!$programDetail) {
            return response()->json(['message' => 'Program not found'], 404);
        }

        $programId = $programDetail->id;

        $query = DB::table('users as players')
            ->join('role_user', 'players.id', '=', 'role_user.user_id')
            ->join('roles', 'role_user.role_id', '=', 'roles.id')
            ->leftJoin('users as guardians', 'players.primary_parent_id', '=', 'guardians.id')
            ->select(
                'players.id as player_id',
                'players.firstName as player_first_name',
                'players.lastName as player_last_name',
                'players.email as player_email',
                'guardians.email as guardian_email'
            )
            ->where('roles.name', '=', 'player')
            ->distinct();

        if ($request->filled('playerFirstName')) {
            $query->where('players.firstName', 'like', '%' . $request->playerFirstName . '%')
                ->orderByRaw("players.firstName LIKE ? DESC", [$request->playerFirstName . '%'])
                ->orderBy('players.firstName');
        }

        if ($request->filled('playerLastName')) {
            $query->where('players.lastName', 'like', '%' . $request->playerLastName . '%')
                ->orderByRaw("players.lastName LIKE ? DESC", [$request->playerLastName . '%'])
                ->orderBy('players.lastName');
        }

        if ($request->filled('playerEmail')) {
            $query->where('players.email', 'like', '%' . $request->playerEmail . '%')
                ->orderByRaw("players.email LIKE ? DESC", [$request->playerEmail . '%'])
                ->orderBy('players.email');
        }

        if ($request->filled('guardianEmail')) {
            $query->where('guardians.email', 'like', '%' . $request->guardianEmail . '%')
                ->orderByRaw("guardians.email LIKE ? DESC", [$request->guardianEmail . '%'])
                ->orderBy('guardians.email');
        }


        $players = $query->paginate(10);



        $registeredPlayers = DB::table('player_program')
            ->where('program_id', $programId)
            ->pluck('player_id');

        $invitedPlayers = DB::table('admin_invites_player_for_program')
            ->where('program_id', $programId)
            ->pluck('user_id');

        $playersCollection = $players->map(function ($player) use ($registeredPlayers, $invitedPlayers) {
            return [
                'id' => $player->player_id,
                'firstName' => $player->player_first_name,
                'lastName' => $player->player_last_name,
                'email' => $player->player_email,
                'guardian_email' => $player->guardian_email,
                'is_registered' => $registeredPlayers->contains($player->player_id),
                'is_invited' =>    $invitedPlayers->contains($player->player_id),
            ];
        });

        $response = [
            'players' => $playersCollection,
            'current_page' => $players->currentPage(),
            'last_page' => $players->lastPage(),
            'total' => $players->total(),
            'message' => $players->total() > 0 ? 'Players found.' : 'No players found.',
            'success' => true,
        ];

        return response()->json($response);
    }

    //TODO complete it, (when updates comes that  how a admin will add a player to program,without payment or with payment)->completed
    //now send mail to the player or may be to the guardian that he has been invited to join the program
    public function addPlayerToProgram(Request $request)
    {
        $user = auth()->user();

        $request->validate([
            'player_id' => 'required',
            'program_slug' => 'required',
        ]);

        $program = Program::where('slug', $request->program_slug)->first();

        $player = User::where('id', $request->player_id)->first();

        if (!$program) {
            abort(404, 'Data not found');
        }

        $invitationExists = DB::table('admin_invites_player_for_program')
            ->where('program_id', $program->id)
            ->where('user_id', $player->id)
            ->exists();

        if (!$invitationExists) {
            DB::table('admin_invites_player_for_program')->insert([
                'user_id' => $player->id,
                'program_id' => $program->id,
                'invited_by' => $user->id,
                'invited_at' => now(),
                'status' => 'pending',
            ]);

            $guardian = User::where('id', $player->primary_parent_id)->first();

            event(new PlayerInvitedByAdmin($guardian, $player, $program));
        }
        return response()->json(['message' => 'Player invited to program  successfully.', 'success' => true], 200);
    }

    public function removePlayerFromProgram(Request $request, $programSlug, $playerId)
    {
        try {
            DB::transaction(function () use ($programSlug, $playerId) {
                $programId = Program::where('slug', $programSlug)->value('id');

                if (!$programId) {
                    abort(404, 'Program not found.');
                }
                PlayerProgram::where('player_id', $playerId)
                    ->where('program_id', $programId)
                    ->delete();

                ProgramRegistration::where('player_id', $playerId)
                    ->where('program_id', $programId)
                    ->delete();
            });

            return redirect()->back()->with('success', 'Player removed from the program successfully.');
        } catch (Exception $e) {
            return redirect()->back()->with('error', 'An error occurred while removing the player. Please try again later.');
        }
    }





    //reports

    public function adminReports()
    {

        return view('admin.reports.reports');
    }



    public function towns()
    {

        $allTowns = User::select('town')
            ->distinct()
            ->whereNotNull('town')
            ->get();


        return response()->json($allTowns);
    }


    public function sport()
    {

        $allSports = [
            ['name' => 'basketball'],
            ['name' => 'volleyball'],
            ['name' => 'pickleball']
        ];
        return response()->json($allSports);
    }


    public function players()
    {
        $players = User::withRole('player')->get();
        return response()->json($players);
    }

    public function genders()
    {

        $genders = [
            ['gender' => 'boys'],
            ['gender' => 'girls'],
        ];
        return response()->json($genders);
    }

    public function programs()
    {

        $programs = Program::all();

        return response()->json($programs);
    }

    public function ages()
    {
        $ageGroups = [
            'adult' => 'Adult',
            'u18' => 'Under 18',
            'u16' => 'Under 16',
            'u14' => 'Under 14',
            'u12' => 'Under 12',
            'u10' => 'Under 10',
            'u8' => 'Under 8',
        ];
        return response()->json($ageGroups);
    }

    public function usersReport(Request $request)
    {
        $users = User::with('roles')->orderBy('created_at', 'desc')->paginate(10);

        $guardianCoachChildren = [];
        $childrens = [];
        $programDetails = [];
        $teamDetails = [];
        $teams = [];
        $isPrimary = [];

        foreach ($users as $user) {

            $childrens[$user->id] = [];
            $programDetails[$user->id] = [];
            $teamDetails[$user->id] = [];
            $teams[$user->id] = [];
            $isPrimary[$user->id] = null;


            if ($user->hasRole('guardian') && $user->hasRole('coach')) {
                $guardianCoachChildren[$user->id] = User::where('parent_id', $user->id)
                    ->orWhere('primary_parent_id', $user->id)
                    ->get();


                $teams = TeamCoach::where('coach_id', $user->id)->get();
                $isPrimary[$user->id] = [];


                foreach ($teams as $teamCoach) {

                    $team = Team::find($teamCoach->team_id);
                    if ($team) {

                        $isPrimary[$user->id][] = [
                            'name' => $team->name,
                            'is_primary' => $teamCoach->is_primary ? 'Primary' : 'Assistant'
                        ];
                    }
                }
            }
            if ($user->hasRole('guardian')) {
                $childrens[$user->id] = User::where('parent_id', $user->id)
                    ->orWhere('primary_parent_id', $user->id)
                    ->get();
            } elseif ($user->hasRole('player')) {
                $programsJoined = ProgramRegistration::where('player_id', $user->id)->get();
                $programIds = $programsJoined->pluck('program_id');
                $programDetails[$user->id] = Program::whereIn('id', $programIds)->paginate(5);


                $teamsJoined = TeamPlayer::where('player_id', $user->id)->get();
                $teamIds = $teamsJoined->pluck('team_id');
                $teamDetails[$user->id] = Team::whereIn('id', $teamIds)->get();

                if ($request->ajax()) {
                    $userId = $request->get('userId');
                    $page = $request->get('page', 1);

                    $programsJoined = ProgramRegistration::where('player_id', $userId)->pluck('program_id');
                    $programDetails = Program::whereIn('id', $programsJoined)->paginate(5, ['*'], 'page', $page);

                    return response()->json([
                        'programs' => $programDetails->items(),
                        'hasMore' => $programDetails->hasMorePages(),
                        'nextPage' => $programDetails->currentPage() + 1,
                    ]);
                }
            } elseif ($user->hasRole('coach')) {

                $teams = TeamCoach::where('coach_id', $user->id)->get();
                $isPrimary[$user->id] = [];


                foreach ($teams as $teamCoach) {

                    $team = Team::find($teamCoach->team_id);

                    if ($team) {

                        $isPrimary[$user->id][] = [
                            'name' => $team->name,
                            'is_primary' => $teamCoach->is_primary ? 'Primary' : 'Assistant'
                        ];
                    }
                }
            }
        }


        return view('admin.reports.usersReport', compact(
            'users',
            'guardianCoachChildren',
            'childrens',
            'programDetails',
            'teamDetails',
            'teams',
            'isPrimary'
        ));
    }



    public function specificReport(Request $request)
    {

        $filters = $request->all();
        $validated = $request->validate([
            'byPlayer' => 'nullable|integer',
            'bySport' => 'nullable|string',
            'byTown' => 'nullable|string',
            'byGender' => 'nullable|string',
            'byProgram' => 'nullable|string',
            'byAge' => 'nullable|string',
            'startDate' => 'nullable|date',
            'endDate' => 'nullable|date',
        ]);


        $activeFilters = array_filter($filters, function ($value) {
            return !is_null($value) && $value !== '';
        });
        $filterCount = count($activeFilters);




        if (isset($activeFilters['startDate']) && isset($activeFilters['endDate'])) {

            $activeFilters['date_range'] = [
                'startDate' => $activeFilters['startDate'],
                'endDate' => $activeFilters['endDate'],
            ];
            unset($activeFilters['startDate'], $activeFilters['endDate']);
        }

        $filterCount = count($activeFilters);

        if ($filterCount === 1) {
            $filterKey = array_key_first($activeFilters);
            $filterValue = $activeFilters[$filterKey];

            return $this->handleSingleFilter($filterKey, $filterValue);
        } elseif ($filterCount === 2) {

            $filterKeys = array_keys($activeFilters);
            $filterValues = array_values($activeFilters);



            return $this->handleTwoFilters($filterKeys, $filterValues);
        } else if ($filterCount == 3 && request()->has('page')) {
            unset($activeFilters['page']);
            $filterKeys = array_keys($activeFilters);
            $filterValues = array_values($activeFilters);



            return $this->handleTwoFilters($filterKeys, $filterValues);
        } else {
            return response()->json(['error' => 'Invalid number of filters'], 400);
        }
    }

    private function handleSingleFilter($filterKey, $filterValue)
    {

        switch ($filterKey) {

            case 'byPlayer':
                $player = User::where('id', $filterValue)->first();
                $playerData = ProgramRegistration::where('player_id', $filterValue)->get();
                $programsOfPlayer = Program::whereIn('id', $playerData->pluck('program_id'))->get();
                return view('admin.reports.specificReport', compact('playerData', 'player', 'programsOfPlayer'));


            case 'bySport':
                $programs = Program::where('sport', $filterValue)->get();
                $programs = $programs->map(function ($program) {
                    $program->registration_count = ProgramRegistration::where('program_id', $program->id)->count();
                    return $program;
                });
                $programsData = ProgramRegistration::whereIn('program_id', $programs->pluck('id'))
                    ->with('player')
                    ->get();
                $programs = $programs->map(function ($program) use ($programsData) {
                    $program->registrations = $programsData->where('program_id', $program->id);
                    return $program;
                });
                return view('admin.reports.specificReport', compact('programs',));



            case 'byTown':
                $playersByTown = User::withRole('player')
                    ->where('town', $filterValue)
                    ->get();
                $playersByTown = $playersByTown->map(function ($player) {

                    $player->programs = ProgramRegistration::where('player_id', $player->id)
                        ->with('program')
                        ->get()
                        ->pluck('program');
                    return $player;
                });
                return view('admin.reports.specificReport', compact(var_name: 'playersByTown'));




            case 'byGender':
                $programsByGender = Program::where('gender', $filterValue)->get();
                $programsByGender = $programsByGender->map(function ($program) {
                    $program->registration_count = ProgramRegistration::where('program_id', $program->id)->count();
                    return $program;
                });
                $programsData = ProgramRegistration::whereIn('program_id', $programsByGender->pluck('id'))
                    ->with('player')
                    ->get();


                $programsByGender = $programsByGender->map(function ($program) use ($programsData) {
                    $program->registrations = $programsData->where('program_id', $program->id);
                    return $program;
                });
                return view('admin.reports.specificReport', compact('programsByGender'));



            case 'byProgram':
                $program = Program::where('name', $filterValue)->first();
                $registrationCount = ProgramRegistration::where('program_id', $program->id)->count();
                $singleProgramData = ProgramRegistration::where('program_id', $program->id)
                    ->with('player')
                    ->get();


                return view('admin.reports.specificReport', compact('singleProgramData', 'program', 'registrationCount'));


            case 'byAge':
                $ageRanges = [
                    'adult' => [19, 100],
                    'u18' => [17, 18],
                    'u16' => [15, 16],
                    'u14' => [13, 14],
                    'u12' => [11, 12],
                    'u10' => [9, 10],
                    'u8' => [7, 8],
                ];

                $startAge = $ageRanges[$filterValue][0] ?? 0;
                $endAge = $ageRanges[$filterValue][1] ?? 0;
                $programsByAge = Program::where('age_restriction_from', '>=', $startAge)
                    ->where('age_restriction_to', '<=', $endAge)
                    ->get();
                $programsByAge = $programsByAge->map(function ($program) {
                    $program->registration_count = ProgramRegistration::where('program_id', $program->id)->count();
                    return $program;
                });
                $programsData = ProgramRegistration::whereIn('program_id', $programsByAge->pluck('id'))
                    ->with('player')
                    ->get();
                $programsByAge = $programsByAge->map(function ($program) use ($programsData) {
                    $program->registrations = $programsData->where('program_id', $program->id);
                    return $program;
                });
                return view('admin.reports.specificReport', compact('programsByAge'));


            case 'byDate':
                //date can't be single i guess, we will look it later;
                break;
            default:
                return response()->json(['error' => 'Invalid filter'], 400);
        }
    }
    //TODO: may be some filters are left complete them .
    private function handleTwoFilters($filterKeys, $filterValues)
    {
        $firstFilter = $filterKeys[0];
        $secondFilter = $filterKeys[1];

        $firstValue = $filterValues[0];
        $secondValue = $filterValues[1];


        if ($firstFilter === 'byPlayer' && $secondFilter === 'date_range') {
            $player = User::where('id', $firstValue)->first();

            $playerData = ProgramRegistration::where('player_id', $firstValue)
                ->whereBetween('created_at', [$secondValue['startDate'], $secondValue['endDate']])
                ->get();
            $playerDataInRange = Program::whereIn('id', $playerData->pluck('program_id'))->get();

            return view('admin.reports.reportWithFilters', compact('playerDataInRange', 'player',));
        }

        if ($firstFilter === 'bySport' && $secondFilter === 'date_range') {
            $programs = Program::where('sport', $firstValue)
                ->where(function ($query) use ($secondValue) {
                    $query->whereBetween('start_date', [$secondValue['startDate'], $secondValue['endDate']])
                        ->orWhereBetween('end_date', [$secondValue['startDate'], $secondValue['endDate']])
                        ->orWhere(function ($query) use ($secondValue) {
                            $query->where('start_date', '<=', $secondValue['startDate'])
                                ->where('end_date', '>=', $secondValue['endDate']);
                        });
                })
                ->get();

            $programs = $programs->map(function ($program) {
                $program->registration_count = ProgramRegistration::where('program_id', $program->id)->count();
                return $program;
            });
            $programsData = ProgramRegistration::whereIn('program_id', $programs->pluck('id'))
                ->with('player')
                ->whereBetween('created_at', [$secondValue['startDate'], $secondValue['endDate']])
                ->get();
            $programsInRange = $programs->map(function ($program) use ($programsData) {
                $program->registrations = $programsData->where('program_id', $program->id);
                return $program;
            });
            return view('admin.reports.reportWithFilters', compact('programsInRange'));
        }

        if ($firstFilter === 'byTown' && $secondFilter === 'byAge') {
            $ageRanges = [
                'adult' => [19, 100],
                'u18' => [17, 18],
                'u16' => [15, 16],
                'u14' => [13, 14],
                'u12' => [11, 12],
                'u10' => [9, 10],
                'u8' => [7, 8],
            ];

            $startAge = $ageRanges[$secondValue][0] ?? 0;
            $endAge = $ageRanges[$secondValue][1] ?? 0;

            $playersByTownWithAge = User::withRole('player')
                ->where('town', $firstValue)->where('age', '>=', $startAge)
                ->where('age', '<=', $endAge)
                ->get();
            $playersByTownWithAge = $playersByTownWithAge->map(function ($player) {

                $player->programs = ProgramRegistration::where('player_id', $player->id)
                    ->with('program')
                    ->get()
                    ->pluck('program');
                return $player;
            });
            return view('admin.reports.reportWithFilters', compact(var_name: 'playersByTownWithAge'));
        }

        if ($firstFilter == 'byTown' && $secondFilter == 'byProgram') {

            $program = Program::where('name', $secondValue)->first();
            if (!$program) {
                return back()->with('error', 'Program not found');
            }
            $singleProgramData = ProgramRegistration::where('program_id', $program->id)
                ->with('player')
                ->get();
            $filteredData = $singleProgramData->filter(function ($registration) use ($firstValue) {
                return $registration->player && $registration->player->town == $firstValue;
            });
            return view('admin.reports.reportWithFilters', compact('filteredData', 'program'));
        }

        if ($firstFilter == 'byTown' && $secondFilter == 'byGender') {
            $normalizedGender = strtolower($secondValue) === 'boys' ? 'boy' : (strtolower($secondValue) === 'girls' ? 'girl' : null);

            if ($normalizedGender) {
                $playersByTownWithGender = User::withRole('player')
                    ->where('town', $firstValue)
                    ->where('gender', $normalizedGender)
                    ->with(['registrations.program'])
                    ->get();
                $playersByTownWithGender->each(function ($player) {
                    $player->programs = $player->registrations->pluck('program');
                });

                return view('admin.reports.reportWithFilters', compact('playersByTownWithGender'));
            } else {
                return redirect()->back()->with('error', 'Invalid gender filter value.');
            }
        }

        if (
            ($firstFilter == 'byTown' && $secondFilter == 'bySport') ||
            ($firstFilter == 'bySport' && $secondFilter == 'byTown')
        ) {

            $townFilter = $firstFilter == 'byTown' ? $firstValue : $secondValue;
            $sportFilter = $firstFilter == 'bySport' ? $firstValue : $secondValue;


            $players = User::withRole('player')->where('town', $townFilter)->get();


            $programs = Program::where('sport', $sportFilter)->get();


            $programRegistrations = ProgramRegistration::whereIn('program_id', $programs->pluck('id'))
                ->with(['program', 'player'])
                ->get();


            $playerIdsWithSport = $programRegistrations->pluck('player_id')->unique();


            $playersWithPrograms = $players->filter(function ($player) use ($playerIdsWithSport) {
                return $playerIdsWithSport->contains($player->id);
            });


            $playersWithPrograms = $playersWithPrograms->map(function ($player) use ($programRegistrations) {
                $playerPrograms = $programRegistrations->filter(function ($registration) use ($player) {
                    return $registration->player_id == $player->id;
                });
                $player->programs = $playerPrograms->pluck('program');
                return $player;
            });

            return view('admin.reports.reportWithFilters', compact('playersWithPrograms'));
        }


        if ($firstFilter == 'byPlayer' && $secondFilter == 'bySport') {
            $player = User::withRole('player')->find($firstValue);

            if (!$player) {
                return back()->with('error', 'Player not found.');
            }
            $programIds = Program::where('sport', $secondValue)->pluck('id');

            $playerWithSport = ProgramRegistration::where('player_id', $player->id)
                ->whereIn('program_id', $programIds)
                ->with('program')
                ->get();

            return view('admin.reports.reportWithFilters', compact('player', 'playerWithSport'));
        }


        if ($firstFilter == "byAge" && $secondFilter == "date_range") {
            $ageRanges = [
                'adult' => [19, 100],
                'u18' => [17, 18],
                'u16' => [15, 16],
                'u14' => [13, 14],
                'u12' => [11, 12],
                'u10' => [9, 10],
                'u8' => [7, 8],
            ];

            $startAge = $ageRanges[$firstValue][0] ?? 0;
            $endAge = $ageRanges[$firstValue][1] ?? 0;
            if (!is_array($secondValue) || !isset($secondValue['startDate']) || !isset($secondValue['endDate'])) {
                return back()->with('error', 'Invalid date range provided.');
            }

            $startDate = $secondValue['startDate'];
            $endDate = $secondValue['endDate'];
            $playersWithDateAndAge = User::withRole('player')
                ->with(['registrations.program'])
                ->whereBetween('age', [$startAge, $endAge])
                ->whereBetween('created_at', [$startDate, $endDate])
                ->paginate(10)->appends(request()->all());

            return view('admin.reports.reportWithFilters', compact('playersWithDateAndAge'));
        }

        if ($firstFilter == "byGender" && $secondFilter == "date_range") {

            $normalizedGender = strtolower($firstValue) === 'boys' ? 'boy' : (strtolower($firstValue) === 'girls' ? 'girl' : null);

            $startDate = $secondValue['startDate'] ?? null;
            $endDate = $secondValue['endDate'] ?? null;

            if ($normalizedGender && $startDate && $endDate) {
                $playersByGenderWithDate = User::withRole('player')
                    ->with(['registrations.program'])
                    ->where('gender', $normalizedGender)
                    ->whereBetween('created_at', [$startDate, $endDate])
                    ->paginate(10)
                    ->appends(request()->all());

                return view('admin.reports.reportWithFilters', compact(var_name: 'playersByGenderWithDate'));
            } else {
                return response()->json(['error' => 'Invalid filters or values'], 400);
            }
        }

        if (
            ($firstFilter == 'byProgram' && $secondFilter == 'byGender') ||
            ($firstFilter == 'byGender' && $secondFilter == 'byProgram')
        ) {
            $programFilter = $firstFilter == 'byProgram' ? $firstValue : $secondValue;
            $genderFilter = $firstFilter == 'byGender' ? $firstValue : $secondValue;


            $normalizedGender = strtolower($genderFilter) === 'boys' ? 'boy' : (strtolower($genderFilter) === 'girls' ? 'girl' : null);

            if ($normalizedGender) {

                $program = Program::where('name', $programFilter)->first();

                if ($program) {

                    $programRegistrations = ProgramRegistration::where('program_id', $program->id)
                        ->with(['program', 'player'])
                        ->get();


                    $playerIdsWithProgram = $programRegistrations->pluck('player_id')->unique();


                    $players = User::withRole('player')
                        ->where('gender', $normalizedGender)
                        ->whereIn('id', $playerIdsWithProgram)
                        ->get();



                    $playersWithProgramsAndGender = $players->map(function ($player) use ($programRegistrations) {
                        $playerPrograms = $programRegistrations->filter(function ($registration) use ($player) {
                            return $registration->player_id == $player->id;
                        });
                        $player->programs = $playerPrograms->pluck('program');
                        return $player;
                    });

                    return view('admin.reports.reportWithFilters', compact('playersWithProgramsAndGender'));
                }
            }

            return response()->json(['error' => 'Invalid filters or values'], 400);
        }

        if (($firstFilter == "byAge" && $secondFilter == "byGender") || ($firstFilter == "byGender" && $secondFilter == "byAge")) {

            $ageFilter = $firstFilter == 'byAge' ? $firstValue : $secondValue;
            $genderFilter = $firstFilter == 'byGender' ? $firstValue : $secondValue;

            $ageRanges = [
                'adult' => [19, 100],
                'u18' => [17, 18],
                'u16' => [15, 16],
                'u14' => [13, 14],
                'u12' => [11, 12],
                'u10' => [9, 10],
                'u8' => [7, 8],
            ];

            $startAge = $ageRanges[$ageFilter][0] ?? 0;
            $endAge = $ageRanges[$ageFilter][1] ?? 0;


            $normalizedGender = strtolower($genderFilter) === 'boys' ? 'boy' : (strtolower($genderFilter) === 'girls' ? 'girl' : null);


            $playersByGenderAndAge = User::withRole('player')
                ->with(['registrations.program'])
                ->where('gender', $normalizedGender)
                ->where('age', '>=', $startAge)
                ->where('age', '<=', $endAge)
                ->paginate(10)
                ->appends(request()->all());


            return view('admin.reports.reportWithFilters', compact('playersByGenderAndAge'));
        }

        if (
            ($firstFilter == 'byGender' && $secondFilter == 'bySport') ||
            ($firstFilter == 'bySport' && $secondFilter == 'byGender')
        ) {
            $sportFilter = $firstFilter == 'bySport' ? $firstValue : $secondValue;
            $genderFilter = $firstFilter == 'byGender' ? $firstValue : $secondValue;

            $normalizedGender = strtolower($genderFilter) === 'boys' ? 'boy' : (strtolower($genderFilter) === 'girls' ? 'girl' : null);

            if (!$normalizedGender) {
                return response()->json(['error' => 'Invalid gender filter value'], 400);
            }


            $playersWithSportAndGender = User::withRole('player')
                ->where('gender', $normalizedGender)
                ->whereHas('registrations.program', function ($query) use ($sportFilter) {
                    $query->where('sport', $sportFilter);
                })
                ->paginate(10)
                ->appends(request()->all());

            return view('admin.reports.reportWithFilters', compact('playersWithSportAndGender'));
        } else {
            return back()->with('error', 'No data available for given filters');
        }
    }




    public function programsReport(Request $request)
    {

        $ageRanges = [
            'adult' => [19, 100],
            'u18' => [17, 18],
            'u16' => [15, 16],
            'u14' => [13, 14],
            'u12' => [11, 12],
            'u10' => [9, 10],
            'u8' => [7, 8],
        ];

        $currentDate = Carbon::today()->toDateString();
        $query = Program::query();

        if ($request->has('sport-filter') && $request->input('sport-filter') != '') {
            $query->where('sport', $request->input('sport-filter'));
        }

        if ($request->has('age-filter') && $request->input('age-filter') != '') {
            $ageGroup = $request->input('age-filter');
            if (isset($ageRanges[$ageGroup])) {
                $ageRange = $ageRanges[$ageGroup];
                $startAge = $ageRange[0];
                $endAge = $ageRange[1];

                $query->where('age_restriction_from',  $startAge)
                    ->where('age_restriction_to',  $endAge);
            }
        }

        if ($request->has('start-date') && $request->input('start-date') != '' && $request->has('end-date') && $request->input('end-date') != '') {




            $startDate = $request->input('start-date');
            $endDate = $request->input('end-date');
            $query->where('start_date', '>=', $startDate)
                ->where('end_date', '<=', $endDate);
        }

        if ($request->has('search-input') && $request->input('search-input') != '') {
            $query->where('name', 'like', '%' . $request->input('search-input') . '%')
                ->orWhere('gender', 'like', '%' . $request->input('search-input') . '%');
        }

        $programs = $query->paginate(3);



        $dayMapping = [
            'mon' => 'M',
            'tue' => 'T',
            'wed' => 'W',
            'thur' => 'Thur',
            'fri' => 'F',
            'sat' => 'S',
            'sun' => 'SU',
        ];

        $programData = [];

        foreach ($programs as $program) {
            // Map frequency days to shortened form
            $formattedFrequencyDays = array_map(function ($day) use ($dayMapping) {
                return $dayMapping[strtolower($day)] ?? $day;
            }, $program->frequency_days);

            if ($program->type == 'Team') {
                $teamRegistrations = ProgramRegistration::where('program_id', $program->id)->get();
                $teamsData = [];
                $payments = GuardianPayment::where('program_id', $program->id)->get();
                $totalPayments = $payments->sum('paid_amount');

                foreach ($teamRegistrations as $registration) {
                    $team = Team::where('id', $registration->team_id)->first();
                    if ($team) {
                        $primaryCoach = TeamCoach::where('team_id', $team->id)
                            ->where('is_primary', true)
                            ->first();

                        $assistantCoach = TeamCoach::where('team_id', $team->id)
                            ->where('is_primary', false)
                            ->first();

                        if ($primaryCoach) {
                            $coachData = User::where('id', $primaryCoach->coach_id)->first();
                            $assistantCoachEmail = $assistantCoach ? User::where("id", $assistantCoach->coach_id)->select('email')->first()->email : null;
                            $playerCount = PlayerInvitation::where('team_id', $team->id)
                                ->where('program_id', $program->id)
                                ->count();

                            $teamBalance = PlayerInvitation::where('team_id', $team->id)
                                ->where('program_id', $program->id)
                                ->get(['balance_assigned', 'balance_due'])
                                ->reduce(function ($carry, $invitation) {
                                    return $carry + ($invitation->balance_assigned - $invitation->balance_due);
                                }, 0);

                            $teamsData[] = [
                                'team_id' => $team->id,
                                'team_name' => $team->name,
                                'coach_slug' => $coachData->slug,
                                'coach_firstName' => $coachData->firstName,
                                'coach_lastName' => $coachData->lastName,
                                'coach_email' => $coachData->email,
                                'assistantCoachEmail' => $assistantCoachEmail,
                                'player_count' => $playerCount,
                                'team_balance' => $teamBalance,
                            ];
                        }
                    }
                }

            $programData[] = [
                'id' => $program->id,
                'sport' => $program->sport,
                'gender' => $program->gender,
                'payment' => $program->payment,
                'frequency_days' => $formattedFrequencyDays,
                'number_of_registers' => $program->number_of_registers,
                'program_slug' => $program->slug,
                'cost' => $program->cost,
                'age_restriction_from' => $program->age_restriction_from,
                'age_restriction_to' => $program->age_restriction_to,
                'start_date' => $program->start_date,
                'end_date' => $program->end_date,
                'frequency' => $program->frequency,
                'name' => $program->name,
                'registrations' => $teamRegistrations->count(),
                'payments' => $totalPayments,
                'type' => $program->type,
                'teams' => $teamsData,
            ];
        } elseif ($program->type == "Individual" || $program->type == "AAU" || $program->type == "Tryout") {
            $individualRegistrations = ProgramRegistration::where('program_id', $program->id)->get();
            $playersData = [];
            $payments = GuardianPayment::where('program_id', $program->id)->get();
            $totalPayments = $payments->sum('paid_amount');


            $invitedPlayersData = [];
            $invitedPlayersRecords = AdminInvitesPlayerForProgram::where('program_id', $program->id)
                ->where('status', 'pending')
                ->get();

            foreach ($invitedPlayersRecords as $invitation) {
                $invitedPlayer = User::find($invitation->user_id);
                if ($invitedPlayer) {
                    $guardian = User::where('id', $invitedPlayer->primary_parent_id)->first();
                    $invitedPlayersData[] = [
                        'id' => $invitedPlayer->id,
                        'first_name' => $invitedPlayer->firstName,
                        'last_name' => $invitedPlayer->lastName,
                        'email' => $invitedPlayer->email,
                        'guardian_email' => $guardian?->email,
                        'status' => $invitation->status
                    ];
                }
            }

            foreach ($individualRegistrations as $registration) {
                $player = User::where('id', $registration->player_id)->first();
                if ($player) {
                    $guardian = User::where('id', $player->primary_parent_id)->first();
                    $playersData[] = [
                        'player_id' => $player->id,
                        'player_name' => $player->firstName . ' ' . $player->lastName,
                        'player_email' => $player->email,
                        'guardian_email' => $guardian?->email,
                        'program_dates' => [
                            'start_date' => $program->start_date,
                            'end_date' => $program->end_date,
                        ],
                    ];
                }
            }

            $programData[] = [
                'id' => $program->id,
                'sport' => $program->sport,
                'gender' => $program->gender,
                'payment' => $program->payment,
                'frequency_days' => $formattedFrequencyDays,
                'number_of_registers' => $program->number_of_registers,
                'program_slug' => $program->slug,
                'age_restriction_from' => $program->age_restriction_from,
                'age_restriction_to' => $program->age_restriction_to,
                'cost' => $program->cost,
                'start_date' => $program->start_date,
                'end_date' => $program->end_date,
                'name' => $program->name,
                'frequency' => $program->frequency,
                'registrations' => $individualRegistrations->count(),
                'payments' => $totalPayments,
                'type' => $program->type,
                'players' => $playersData,
                'invited_players' => $invitedPlayersData,
            ];
        }
    }

        if ($request->ajax()) {
            return response()->json([
                'programData' => view('admin.reports.partialProgramReport', compact('programData', 'programs'))->render(),
                'pagination' => $programs->appends($request->query())->links('pagination::bootstrap-5')->render(),
            ]);
        }

        return view('admin.reports.programsReport', compact('programData', 'programs'));
    }


    public function financialReport(Request $request)
    {

        $towns = User::whereNotNull('town')->distinct()->pluck('town');


        $query = DB::table('all_payments');


        if ($request->has('town-filter') && $request->input('town-filter') !== '') {
            $query->where('town', $request->input('town-filter'));
        }

        if ($request->has('sport-filter') && $request->input('sport-filter') !== '') {
            $programIds = DB::table('programs')
                ->where('sport', $request->input('sport-filter'))
                ->pluck('id')
                ->map(function ($id) {
                    return '"' . $id . '"';
                })
                ->toArray();

            $query->where(function ($q) use ($programIds) {
                foreach ($programIds as $programId) {
                    $q->orWhere('program_ids', 'LIKE', '%' . $programId . '%');
                }
            });
        }

        if ($request->has('start-date') && $request->input('start-date') != '' && $request->has('end-date') && $request->input('end-date') != '') {
            $startDate = Carbon::parse($request->input('start-date'))->startOfDay();
            $endDate = Carbon::parse($request->input('end-date'))->endOfDay();
            $query->whereBetween('created_at', [$startDate, $endDate]);
        }

        if ($request->has('age-filter') && $request->input('age-filter') != '') {
            $ageRanges = [
                'adult' => [19, 100],
                'u18' => [17, 18],
                'u16' => [15, 16],
                'u14' => [13, 14],
                'u12' => [11, 12],
                'u10' => [9, 10],
                'u8' => [7, 8],
            ];

            $ageGroup = $request->input('age-filter');

            if (isset($ageRanges[$ageGroup])) {
                [$startAge, $endAge] = $ageRanges[$ageGroup];

                $programIds = DB::table('programs')
                    ->where('age_restriction_from', '=', $startAge)
                    ->where('age_restriction_to', '=', $endAge)
                    ->pluck('id') // Get only the `id` column
                    ->map(function ($id) {
                        return '"' . $id . '"';
                    })
                    ->toArray();

                if (!empty($programIds)) {
                    $query->where(function ($q) use ($programIds) {
                        foreach ($programIds as $programId) {
                            $q->orWhere('program_ids', 'LIKE', '%' . $programId . '%');
                        }
                    });
                } else {

                    $query->whereRaw('0 = 1');
                }
            }
        }
        $query->orderBy('created_at', 'desc');
        $allPayments = $query->paginate(30);
        if ($request->hasAny(['town-filter', 'sport-filter', 'start-date', 'end-date', 'age-filter'])) {
            $allPayments->appends($request->only([
                'town-filter',
                'sport-filter',
                'start-date',
                'end-date',
                'age-filter'
            ]));
        }
        $allPayments->transform(function ($payment) {
            // Handle player_ids decoding
            $rawPlayerIds = $payment->player_ids;
            $firstDecodedPlayerIds = json_decode($rawPlayerIds, true);

            if (is_string($firstDecodedPlayerIds)) {
                $playerIds = json_decode($firstDecodedPlayerIds, true);
            } else {
                $playerIds = is_array($firstDecodedPlayerIds) ? $firstDecodedPlayerIds : [];
            }

            $rawProgramIds = json_decode($payment->program_ids, true);
            $programIds = is_array($rawProgramIds) ? $rawProgramIds : [$rawProgramIds];


            $programIds = array_map(function ($id) {
                return intval(trim($id, '"'));
            }, $programIds);


            $user = User::find($payment->user_id);
            $players = User::whereIn('id', $playerIds)->get();
            $programs = Program::whereIn('id', $programIds)->get();

            $payment->guardian = $user;
            $payment->players = $players;
            $payment->programs = $programs;

            return $payment;
        });

 if ($request->ajax()) {
    return response()->json([
        'allPayments' => view('admin.reports.partialFinancialReport', compact('allPayments', 'towns'))->render(),
        'pagination' => $allPayments->links('pagination::bootstrap-5')->render(),
    ]);
}
    return view('admin.reports.financialReport', compact('towns', 'allPayments'));
}





}
