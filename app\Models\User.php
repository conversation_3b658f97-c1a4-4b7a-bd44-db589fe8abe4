<?php

namespace App\Models;


use Illuminate\Contracts\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Foundation\Auth\User as Authenticatable;
use Illuminate\Notifications\Notifiable;
use Carbon\Carbon;
use Lara<PERSON>\Sanctum\HasApiTokens;
use Illuminate\Support\Str;

class User extends Authenticatable
{
    use HasApiTokens,HasFactory, Notifiable;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'firstName',
        'lastName',
        'email',
        'password',
        'birthDate',
        'age',
        'profilePhoto',
         'grade',
         'gender',
         'address',
         'street',
         'town',
         'state',
         'program',
         'teamName',
         'parent_id',
         'is_joined',
         'primary_parent_id',
         'mobile_number',
         'is_guardian',
         'is_coach',
         'current_role',
         'stripe_customer_id'
    ];

    /**
     * The attributes that should be hidden for serialization.
     *
     * @var array<int, string>
     */
    protected $hidden = [
        'password',
        'remember_token',
    ];

    /**
     * Get the attributes that should be cast.
     *
     * @return array<string, string>
     */
    protected function casts(): array
    {
        return [
            'email_verified_at' => 'datetime',
            'password' => 'hashed',
        ];
    }

 public static function boot()
    {
        parent::boot();

        static::creating(function ($user) {

            $fullName = trim($user->firstName . ' ' . $user->lastName);
            $slug = Str::slug($fullName);


            $count = User::where('slug', $slug)->count();
            if ($count > 0) {
                $slug = $slug . '-' . ($count + 1);
            }

            $user->slug = $slug;
        });
    }







    public function additional_guardians(){
        return $this->hasMany(User::class, 'parent_id', 'id')->whereHas('roles', function ($query) {
            $query->where('name', 'guardian');
        });
    }

    public function players(){
        //guardian players
        return $this->hasMany(User::class, 'parent_id', 'id')->whereHas('roles', function ($query) {
            $query->where('name', 'player');
        });
    }

    public function all_players(){
        //guardian + additional_guardians players
       return $this->hasMany(User::class, 'primary_parent_id', 'id')->whereHas('roles', function ($query) {
            $query->where('name', 'player');
        });
    }

      public function roles()
    {
        return $this->belongsToMany(Role::class, 'role_user');
    }

    public function hasRole($role){
        return in_array($role, $this->roles()->pluck('name')->toArray());
    }

     public function scopeWithRole($query, $roleName)
    {
        return $query->whereHas('roles', function ($q) use ($roleName) {
            $q->where('name', $roleName);
        });
    }



    public function program(){
        return $this->hasMany(UserProgram::class);
    }


public function calculateAmountForProgram($programId, $registerDate = null)
{
    try {
        $program = Program::findOrFail($programId);


        if (!$registerDate) {
            $registerDate = Carbon::now();
        }
        if ($program->enable_early_bird_specials == 0) {
            return $program->cost;
        }

        $earlyBirdPricings = EarlyBirdPricing::where('program_id', $program->id)->get();
        $totalRegistration = ProgramRegistration::where('program_id', $program->id)->count() + 1;

        $price = null;

        if ($program->early_bird_specials_date) {
            foreach ($earlyBirdPricings as $earlyBirdPricing) {
                if ($totalRegistration >= $earlyBirdPricing->from && $totalRegistration <= $earlyBirdPricing->to) {
                    if ($registerDate->lessThan($program->early_bird_specials_date)) {
                        $price = $earlyBirdPricing->price_before;
                    } else {
                        $price = $earlyBirdPricing->price_on_or_after;
                    }
                    break;
                }
            }
        } else {
            foreach ($earlyBirdPricings as $earlyBirdPricing) {
                if ($totalRegistration >= $earlyBirdPricing->from && $totalRegistration <= $earlyBirdPricing->to) {
                    $price = $earlyBirdPricing->price_before;
                    break;
                } else {
                    $price = $earlyBirdPricing->price_on_or_after;
                    break;
                }
            }
        }
        return $price ?? $program->cost;

    } catch (\Exception $e) {
        return redirect()->back()->withErrors(['error' => 'An error occurred. Please try again later.']);
    }
}









    public function teams()
    {
        return $this->belongsToMany(Team::class, 'team_player', 'player_id', 'team_id');
    }


   public function programs()
{
    return $this->hasMany(PlayerProgram::class, 'player_id');
}

public function registrations()
{
    return $this->hasMany(ProgramRegistration::class, 'player_id');
}

public function programRegistrations()
{
    return $this->belongsToMany(Program::class, 'program_registrations');
}





}
