<div class="container">
    <div class="heading text-center mb-5">
        <h2 class="text-uppercase fs-6 mb-0">All Users</h2>
    </div>

    {{-- Livewire Filter Form --}}
    <div class="form row table-filter justify-content-center">
        <div class="col-md-auto">
            <div class="filter-option">
                <div class="mb-4">
                    <label class="form-label text-uppercase" for="filter">Filter By</label>
                    <div class="select-arrow position-relative">
                        <select class="form-control" id="filter" wire:model.live="filter">
                            <option value="">All</option>
                            @foreach ($this->getAvailableFilters() as $filterOption)
                                <option value="{{ $filterOption }}">{{ $filterOption }}</option>
                            @endforeach
                        </select>
                        <span class="arrow"><i class="bi bi-chevron-down"></i></span>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-auto">
            <div class="filter-option">
                <div class="mb-4">
                    <label class="form-label text-uppercase" for="search">Search</label>
                    <div class="flex items-center">
                        <input class="form-control" id="search" type="text"
                            wire:model.live.debounce.500ms="search" placeholder="Search users..." />
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-auto">
            <div class="filter-option">
                <div class="mb-4">
                    <label class="form-label text-uppercase" for="perPage">Per Page</label>
                    <div class="select-arrow position-relative">
                        <select class="form-control" id="perPage" wire:model.live="perPage">
                            <option value="5">5</option>
                            <option value="10">10</option>
                            <option value="25">25</option>
                            <option value="50">50</option>
                        </select>
                        <span class="arrow"><i class="bi bi-chevron-down"></i></span>
                    </div>
                </div>
            </div>
        </div>
    </div>


    <div class="container">
        <div>
            <button id="sendEmailButton" class="cta"> Send Email</button>
        </div>
    </div>

    {{-- Users Table --}}
    <div class="program-table">
        <div class="table-program table-responsive" wire:loading.class="table-updating">
            <table class="table table-hover" width="100%" id="user-table">
                <thead>
                    <tr>
                        <th class="py-4">First Name</th>
                        <th class="py-4">Last Name</th>
                        <th class="py-4">User Type</th>
                        <th class="py-4">Email</th>
                        <th class="py-4">Town</th>
                        <th class="py-4">Age</th>
                        <th class="py-4">Grade</th>
                        <th class="py-4" width="20">Edit</th>
                        <th class="py-4" width="20">Delete</th>
                    </tr>
                </thead>
                <tbody>
                    @forelse ($users as $user)
                        <tr wire:key="user-{{ $user->id }}">
                            <td class="py-4" valign="middle">{{ $user->firstName }}</td>
                            <td class="py-4" valign="middle">{{ $user->lastName }}</td>
                            <td class="py-4" valign="middle">
                                @foreach ($user->roles as $role)
                                    {{ $role->name }}@if (!$loop->last)
                                        ,
                                    @endif
                                @endforeach
                            </td>
                            <td class="py-4" valign="middle">{{ $user->email ?? 'N/A' }}</td>
                            <td class="py-4" valign="middle">{{ $user->town ?? 'N/A' }}</td>
                            <td class="py-4" valign="middle">{{ $user->age ?? 'N/A' }}</td>
                            <td class="py-4" valign="middle">{{ $user->grade ?? 'N/A' }}</td>

                            @if ($user->hasRole('admin'))
                                <td class="py-4" valign="middle">
                                    <a href="" class="action edit" id="edit-admin"
                                        data-admin-id="{{ $user->id }}">
                                        <img src="{{ asset('images/edit-icon.svg') }}" alt="Edit" width="20"
                                            height="20" />
                                    </a>
                                </td>
                            @elseif($user->hasRole('coach'))
                                <td class="py-4" valign="middle">
                                    <a href="" class="action edit" id="edit-coach"
                                        data-coach-id="{{ $user->id }}">
                                        <img src="{{ asset('images/edit-icon.svg') }}" alt="Edit" width="20"
                                            height="20" />
                                    </a>
                                </td>
                            @elseif($user->hasRole('guardian'))
                                <td class="py-4" valign="middle">
                                    <a href="" id="edit-guardian" data-guardian-id="{{ $user->id }}"
                                        class="action edit">
                                        <img src="{{ asset('images/edit-icon.svg') }}" alt="Edit" width="20"
                                            height="20" />
                                    </a>
                                </td>
                            @elseif($user->hasRole('player'))
                                <td class="py-4" valign="middle">
                                    <a href="" class="action edit" id="edit-player"
                                        data-player-id="{{ $user->id }}">
                                        <img src="{{ asset('images/edit-icon.svg') }}" alt="Edit" width="20"
                                            height="20" />
                                    </a>
                                </td>
                            @endif

                            <td class="py-4" valign="middle">
                                <form id="deleteUser-{{ $user->id }}"
                                    onsubmit="showConfirmation(event, 'deleteUser-{{ $user->id }}')"
                                    action="{{ route('admin.destroy', ['user' => $user->id]) }}" method="POST"
                                    class="inline-form mb-0">
                                    @csrf
                                    @method('DELETE')
                                    <button type="submit" class="action edit bg-transparent border-0 p-0">
                                        <img src="{{ asset('images/delete-icon.svg') }}" alt="Delete"
                                            width="18" height="20" />
                                    </button>
                                </form>
                            </td>
                        </tr>
                        @empty
                            <tr>
                                <td colspan="9" class="text-center py-4">
                                    @if ($search || $filter)
                                        <p class="mb-0">No users found matching your search criteria.</p>
                                        <button class="btn btn-link p-0" wire:click="clearFilters">Clear filters</button>
                                    @else
                                        <p class="mb-0">No users found.</p>
                                    @endif
                                </td>
                            </tr>
                        @endforelse
                    </tbody>
                </table>

                {{-- Custom Livewire Pagination --}}
                <div class="mt-4 d-flex justify-content-center">
                    {{ $users->onEachSide(1)->links('custom-pagination') }}
                </div>
            </div>
        </div>
    </div>
